<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * विद्यापति पुस्तकालय - अध्ययन तथा अनुसंधान केन्द्र
 * Vidyapati Library - Study and Research Center
 * 
 * Main Entry Point for Public Interface
 */

require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/functions.php';

// Initialize database connection
$db = new Database();
$conn = $db->getConnection();

// Get current page
$page = $_GET['page'] ?? 'home';

// Handle different pages
switch ($page) {
    case 'catalog':
        include 'pages/catalog.php';
        exit;
    case 'gallery':
        include 'pages/gallery.php';
        exit;
    case 'book':
        include 'pages/book-detail.php';
        exit;
    case 'about':
        include 'pages/about.php';
        exit;
    case 'contact':
        include 'pages/contact.php';
        exit;
    case 'privacy':
        include 'pages/privacy.php';
        exit;
    case 'terms':
        include 'pages/terms.php';
        exit;
    case 'home':
    default:
        // Continue with home page
        break;
}




// Get featured books (latest 6 books) with enhanced information
$featured_books_sql = "SELECT b.*,
                              a.name as author_name, a.name<PERSON><PERSON><PERSON> as author_name_nepali,
                              c.name as category_name, c.nameNepali as category_name_nepali,
                              l.name as language_name, l.nameNepali as language_name_nepali,
                              p.name as publisher_name, p.nameNepali as publisher_name_nepali,
                              loc.shelf as location_shelf, loc.row as location_row,
                              CASE
                                  WHEN p.name IS NOT NULL THEN p.name
                                  WHEN p.nameNepali IS NOT NULL THEN p.nameNepali
                                  ELSE b.publisher
                              END as display_publisher
                       FROM books b
                       LEFT JOIN authors a ON b.authorId = a.id
                       LEFT JOIN categories c ON b.categoryId = c.id
                       LEFT JOIN languages l ON b.languageId = l.id
                       LEFT JOIN publishers p ON b.publisherId = p.id
                       LEFT JOIN locations loc ON b.locationId = loc.id
                       WHERE b.isDeleted = 0
                       ORDER BY b.createdAt DESC
                       LIMIT 6";

$featured_books = $conn->query($featured_books_sql)->fetchAll(PDO::FETCH_ASSOC);

// Page title and meta
$page_title = 'मैथिली विकास कोष - Maithili Vikas Kosh';
$page_description = 'Digital catalog of Maithili literature and cultural heritage | विद्यापति पुस्तकालय - अध्ययन तथा अनुसंधान केन्द्र';

include 'templates/header.php';
?>

<main class="main-content">
    <!-- Hero Section -->
    <section class="hero-section">
        <!-- Floating Icons -->
        <div class="hero-icons">
            <div class="hero-icon">📚</div>
            <div class="hero-icon">📖</div>
            <div class="hero-icon">✍️</div>
            <div class="hero-icon">🏛️</div>
        </div>

        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="nepali-text">मैथिली विकास कोष</span>
                    <span class="english-text">Maithili Vikas Kosh</span>
                </h1>
                <h2 class="hero-subtitle">
                    <span class="nepali-text">विद्यापति पुस्तकालय - अध्ययन तथा अनुसंधान केन्द्र</span>
                    <span class="english-text">Vidyapati Library - Study and Research Center</span>
                </h2>
                <p class="hero-description">
                    Discover the treasures of Maithili literature and cultural heritage. Our comprehensive digital library preserves and celebrates the rich literary traditions of Mithila, making them accessible to scholars, researchers, and enthusiasts worldwide.
                </p>



                <div class="hero-actions">
                    <a href="?page=catalog" class="btn btn-primary">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                        Browse Books
                    </a>
                    <a href="?page=gallery" class="btn btn-secondary">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                        </svg>
                        View Gallery
                    </a>
                </div>
            </div>
        </div>
    </section>


    <!-- Quick Search -->
    <section class="quick-search-section">
        <div class="container">
            <form method="GET" action="?page=catalog" class="quick-search-form">
                <div class="quick-search-input-group">
                    <input type="text" name="search" placeholder="Quick search: books, authors, titles..." class="quick-search-input">
                    <button type="submit" class="quick-search-btn">
                        <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Featured Books -->
    <section class="featured-books-section">
        <div class="container">
            <div class="section-header">
                <h3 class="section-title">
                    <span class="nepali-text">नवीनतम पुस्तकहरू</span>
                    <span class="english-text">Latest Additions</span>
                </h3>
                <p class="section-description">Discover our newest additions to the collection</p>
            </div>

            <?php if (!empty($featured_books)): ?>
                <div class="books-grid">
                    <?php foreach ($featured_books as $book): ?>
                        <div class="book-card">
                            <div class="book-image">
                                <?php
                                $image_path = "uploads/books/" . $book['id'] . ".jpg";
                                if (file_exists($image_path)):
                                ?>
                                    <img src="<?= $image_path ?>?v=<?= filemtime($image_path) ?>"
                                         alt="<?= htmlspecialchars($book['title']) ?>"
                                         loading="lazy">
                                <?php else: ?>
                                    <div class="book-placeholder">
                                        <svg class="book-icon" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="book-info">
                                <h4 class="book-title">
                                    <?= htmlspecialchars($book['titleNepali'] ?: $book['title']) ?>
                                </h4>

                                <div class="book-details">
                                    <div class="book-detail-item">
                                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                        </svg>
                                        <span class="book-author">
                                            <?= htmlspecialchars($book['author_name_nepali'] ?: $book['author_name']) ?>
                                        </span>
                                    </div>

                                    <div class="book-detail-item">
                                        <svg class="detail-icon" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
                                        </svg>
                                        <span class="book-category">
                                            <?= htmlspecialchars($book['category_name_nepali'] ?: $book['category_name']) ?>
                                        </span>
                                    </div>

                                    <div class="book-meta-row">
                                        <div class="book-meta-item">
                                            <svg class="detail-icon" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H18V1h-2v1H8V1H6v1H4.5C3.67 2 3 2.67 3 3.5v15c0 .83.67 1.5 1.5 1.5h15c.83 0 1.5-.67 1.5-1.5v-15c0-.83-.67-1.5-1.5-1.5zM19 19H5V8h14v11z"/>
                                            </svg>
                                            <span class="book-year"><?= $book['publishedYear'] ?></span>
                                        </div>

                                        <?php if ($book['pages']): ?>
                                        <div class="book-meta-item">
                                            <svg class="detail-icon" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                            </svg>
                                            <span class="book-pages"><?= number_format($book['pages']) ?> pages</span>
                                        </div>
                                        <?php endif; ?>

                                        <?php if ($book['location_shelf']): ?>
                                        <div class="book-meta-item">
                                            <svg class="detail-icon" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                                            </svg>
                                            <span class="book-location">
                                                <?= htmlspecialchars($book['location_shelf']) ?><?= $book['location_row'] ? '-' . htmlspecialchars($book['location_row']) : '' ?>
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <a href="?page=book&id=<?= $book['id'] ?>" class="book-link">
                                    <svg class="link-icon" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                                    </svg>
                                    View Details
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="section-footer">
                    <a href="?page=catalog" class="btn btn-primary">View All Books</a>
                </div>
            <?php else: ?>
                <div class="no-results">
                    <div class="no-results-icon">📚</div>
                    <h4>No books available</h4>
                    <p>Books will appear here once they are added to the collection.</p>
                </div>
            <?php endif; ?>
        </div>
    </section>
</main>

<?php include 'templates/footer.php'; ?>
