<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Common Functions
 */

/**
 * Generate unique ID (similar to Prisma's cuid)
 */
function generate_id($prefix = '') {
    return $prefix . uniqid() . bin2hex(random_bytes(4));
}

/**
 * Sanitize filename for uploads
 */
function sanitize_filename($filename) {
    // Remove any path info
    $filename = basename($filename);
    
    // Remove special characters except dots and hyphens
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    
    // Remove multiple consecutive underscores
    $filename = preg_replace('/_+/', '_', $filename);
    
    // Trim underscores from start and end
    $filename = trim($filename, '_');
    
    return $filename;
}

/**
 * Get file extension
 */
function get_file_extension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Check if file is allowed image type
 */
function is_allowed_image($filename) {
    $extension = get_file_extension($filename);
    return in_array($extension, ALLOWED_IMAGE_TYPES);
}

/**
 * Resize and optimize image
 */
function resize_image($source_path, $destination_path, $max_width, $max_height, $quality = 85) {
    if (!file_exists($source_path)) {
        return false;
    }
    
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return false;
    }
    
    list($orig_width, $orig_height, $image_type) = $image_info;
    
    // Calculate new dimensions
    $ratio = min($max_width / $orig_width, $max_height / $orig_height);
    $new_width = round($orig_width * $ratio);
    $new_height = round($orig_height * $ratio);
    
    // Create image resource from source
    switch ($image_type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_WEBP:
            $source_image = imagecreatefromwebp($source_path);
            break;
        default:
            return false;
    }
    
    if (!$source_image) {
        return false;
    }
    
    // Create new image
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    // Preserve transparency for PNG and WebP
    if ($image_type == IMAGETYPE_PNG || $image_type == IMAGETYPE_WEBP) {
        imagealphablending($new_image, false);
        imagesavealpha($new_image, true);
        $transparent = imagecolorallocatealpha($new_image, 255, 255, 255, 127);
        imagefill($new_image, 0, 0, $transparent);
    }
    
    // Resize image
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $orig_width, $orig_height);
    
    // Save image
    $result = false;
    $destination_ext = get_file_extension($destination_path);
    
    switch ($destination_ext) {
        case 'jpg':
        case 'jpeg':
            $result = imagejpeg($new_image, $destination_path, $quality);
            break;
        case 'png':
            $result = imagepng($new_image, $destination_path, round(9 * (100 - $quality) / 100));
            break;
        case 'webp':
            $result = imagewebp($new_image, $destination_path, $quality);
            break;
    }
    
    // Clean up memory
    imagedestroy($source_image);
    imagedestroy($new_image);
    
    return $result;
}

/**
 * Upload and process book image
 */
function upload_book_image($file, $book_id) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'error' => 'No file uploaded'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'error' => 'Upload error: ' . $file['error']];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'error' => 'File too large. Maximum size: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB'];
    }
    
    if (!is_allowed_image($file['name'])) {
        return ['success' => false, 'error' => 'Invalid file type. Allowed: ' . implode(', ', ALLOWED_IMAGE_TYPES)];
    }
    
    // Create upload directories if they don't exist
    $upload_dir = UPLOAD_DIR . 'books/';
    $thumb_dir = UPLOAD_DIR . 'thumbs/';
    
    if (!is_dir($upload_dir)) mkdir($upload_dir, 0755, true);
    if (!is_dir($thumb_dir)) mkdir($thumb_dir, 0755, true);
    
    // Generate filenames
    $original_path = $upload_dir . $book_id . '.jpg';
    $thumbnail_path = $thumb_dir . $book_id . '.jpg';
    
    // Resize and save original (max 800x1200)
    if (!resize_image($file['tmp_name'], $original_path, 800, 1200, IMAGE_QUALITY)) {
        return ['success' => false, 'error' => 'Failed to process image'];
    }
    
    // Create thumbnail
    if (!resize_image($original_path, $thumbnail_path, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT, THUMBNAIL_QUALITY)) {
        // Thumbnail creation failed, but original succeeded
        log_activity("Thumbnail creation failed for book: " . $book_id, 'WARNING');
    }
    
    return [
        'success' => true,
        'original_path' => $original_path,
        'thumbnail_path' => $thumbnail_path
    ];
}

/**
 * Delete book image files
 */
function delete_book_image($book_id) {
    $original_path = UPLOAD_DIR . 'books/' . $book_id . '.jpg';
    $thumbnail_path = UPLOAD_DIR . 'thumbs/' . $book_id . '.jpg';
    
    $deleted = [];
    
    if (file_exists($original_path)) {
        $deleted['original'] = unlink($original_path);
    }
    
    if (file_exists($thumbnail_path)) {
        $deleted['thumbnail'] = unlink($thumbnail_path);
    }
    
    return $deleted;
}

/**
 * Get book image URL
 */
function get_book_image_url($book_id, $thumbnail = false) {
    $dir = $thumbnail ? 'thumbs/' : 'books/';
    $file_path = UPLOAD_DIR . $dir . $book_id . '.jpg';
    
    if (file_exists($file_path)) {
        return UPLOAD_URL . $dir . $book_id . '.jpg?v=' . filemtime($file_path);
    }
    
    return null;
}

/**
 * Format file size
 */
function format_file_size($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Generate pagination HTML
 */
function generate_pagination($current_page, $total_pages, $base_url, $params = []) {
    if ($total_pages <= 1) return '';
    
    $html = '<div class="pagination">';
    
    // Previous button
    if ($current_page > 1) {
        $prev_params = array_merge($params, ['p' => $current_page - 1]);
        $prev_url = $base_url . '?' . http_build_query($prev_params);
        $html .= '<a href="' . htmlspecialchars($prev_url) . '" class="pagination-btn">← Previous</a>';
    }
    
    // Page numbers
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $page_params = array_merge($params, ['p' => $i]);
        $page_url = $base_url . '?' . http_build_query($page_params);
        $active_class = ($i === $current_page) ? ' active' : '';
        $html .= '<a href="' . htmlspecialchars($page_url) . '" class="pagination-btn' . $active_class . '">' . $i . '</a>';
    }
    
    // Next button
    if ($current_page < $total_pages) {
        $next_params = array_merge($params, ['p' => $current_page + 1]);
        $next_url = $base_url . '?' . http_build_query($next_params);
        $html .= '<a href="' . htmlspecialchars($next_url) . '" class="pagination-btn">Next →</a>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Truncate text with ellipsis
 */
function truncate_text($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * Clean and validate input
 */
function clean_input($input, $type = 'string') {
    if (is_array($input)) {
        return array_map(function($item) use ($type) {
            return clean_input($item, $type);
        }, $input);
    }
    
    $input = trim($input);
    
    switch ($type) {
        case 'int':
            return (int) filter_var($input, FILTER_SANITIZE_NUMBER_INT);
        case 'float':
            return (float) filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
        case 'email':
            return filter_var($input, FILTER_SANITIZE_EMAIL);
        case 'url':
            return filter_var($input, FILTER_SANITIZE_URL);
        case 'string':
        default:
            return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Validate required fields
 */
function validate_required_fields($data, $required_fields) {
    $errors = [];
    
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
        }
    }
    
    return $errors;
}

/**
 * Generate random password
 */
function generate_password($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

/**
 * Hash password
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Send JSON response
 */
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Redirect with message
 */
function redirect_with_message($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header('Location: ' . $url);
    exit;
}

/**
 * Get and clear flash message
 */
function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = [
            'text' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type'] ?? 'info'
        ];
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return $message;
    }
    return null;
}

/**
 * Format time ago
 */
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

/**
 * Check if string contains Devanagari characters
 */
function has_devanagari($text) {
    return preg_match('/[\x{0900}-\x{097F}]/u', $text);
}

/**
 * Auto-detect and format bilingual text
 */
function format_bilingual_text($text) {
    if (has_devanagari($text)) {
        return '<span class="nepali-text">' . htmlspecialchars($text) . '</span>';
    } else {
        return '<span class="english-text">' . htmlspecialchars($text) . '</span>';
    }
}

/**
 * Check if Devanagari fonts are properly loaded
 */
function check_font_availability() {
    $fonts_dir = __DIR__ . '/../assets/fonts/';
    $required_fonts = [
        'NotoSansDevanagari-Regular.ttf',
        'NotoSansDevanagari-Bold.ttf'
    ];

    $status = [
        'all_loaded' => true,
        'fonts' => []
    ];

    foreach ($required_fonts as $font) {
        $font_path = $fonts_dir . $font;
        $exists = file_exists($font_path);
        $status['fonts'][$font] = [
            'exists' => $exists,
            'path' => $font_path,
            'size' => $exists ? filesize($font_path) : 0
        ];

        if (!$exists) {
            $status['all_loaded'] = false;
        }
    }

    return $status;
}

/**
 * Generate CSS for font preloading
 */
function generate_font_preload_css() {
    $css = '';
    $fonts = [
        'NotoSansDevanagari-Regular.ttf' => 'font/ttf',
        'NotoSansDevanagari-Bold.ttf' => 'font/ttf'
    ];

    foreach ($fonts as $font_file => $mime_type) {
        $font_url = font_url($font_file);
        $css .= "<link rel=\"preload\" href=\"{$font_url}\" as=\"font\" type=\"{$mime_type}\" crossorigin>\n";
    }

    return $css;
}

/**
 * Get appropriate font class based on text content
 */
function get_font_class($text, $default = 'english-text') {
    if (empty($text)) {
        return $default;
    }

    // Check for Devanagari characters (including Maithili)
    if (preg_match('/[\x{0900}-\x{097F}]/u', $text)) {
        return 'nepali-text';
    }

    // Check for mixed content
    if (preg_match('/[\x{0900}-\x{097F}]/u', $text) && preg_match('/[a-zA-Z]/', $text)) {
        return 'mixed-text';
    }

    return $default;
}

/**
 * Format text with appropriate font styling
 */
function format_text_with_font($text, $tag = 'span', $additional_classes = '') {
    if (empty($text)) {
        return '';
    }

    $font_class = get_font_class($text);
    $classes = trim($font_class . ' ' . $additional_classes);

    return "<{$tag} class=\"{$classes}\">" . htmlspecialchars($text) . "</{$tag}>";
}

/**
 * Generate icon HTML element
 */
function render_icon($size = 'medium', $alt = null, $classes = '') {
    $alt_text = $alt ?: APP_NAME . ' Icon';
    $icon_classes = "icon icon-{$size} app-icon {$classes}";
    $icon_url = icon_url($size === 'small' ? 16 : ($size === 'medium' ? 24 : ($size === 'large' ? 32 : ($size === 'xlarge' ? 48 : 64))));

    return "<img src=\"{$icon_url}\" alt=\"{$alt_text}\" class=\"{$icon_classes}\">";
}

/**
 * Get library statistics for display
 */
function get_library_statistics($db) {
    try {
        $stats = [
            'books' => $db->fetchColumn("SELECT COUNT(*) FROM books WHERE isDeleted = 0") ?: 0,
            'categories' => $db->fetchColumn("SELECT COUNT(*) FROM categories WHERE isDeleted = 0") ?: 0,
            'authors' => $db->fetchColumn("SELECT COUNT(*) FROM authors WHERE isDeleted = 0") ?: 0,
            'publishers' => $db->fetchColumn("SELECT COUNT(*) FROM publishers WHERE isDeleted = 0") ?: 0,
            'languages' => $db->fetchColumn("SELECT COUNT(*) FROM languages WHERE isDeleted = 0") ?: 0
        ];

        return $stats;
    } catch (Exception $e) {
        // Return fallback statistics if database error
        return [
            'books' => 0,
            'categories' => 0,
            'authors' => 0,
            'publishers' => 0,
            'languages' => 0
        ];
    }
}

/**
 * Render horizontal statistics section
 */
function render_statistics_section($stats, $css_class = 'page-stats', $show_all = true) {
    $html = '<div class="' . htmlspecialchars($css_class) . '">';

    // Always show books
    $html .= '<div class="stat-item">';
    $html .= '<strong>' . number_format($stats['books']) . '</strong>';
    $html .= '<span class="stat-label">Total Books</span>';
    $html .= '</div>';

    if ($show_all) {
        $html .= '<div class="stat-item">';
        $html .= '<strong>' . number_format($stats['categories']) . '</strong>';
        $html .= '<span class="stat-label">Categories</span>';
        $html .= '</div>';

        $html .= '<div class="stat-item">';
        $html .= '<strong>' . number_format($stats['authors']) . '</strong>';
        $html .= '<span class="stat-label">Authors</span>';
        $html .= '</div>';

        $html .= '<div class="stat-item">';
        $html .= '<strong>' . number_format($stats['publishers']) . '</strong>';
        $html .= '<span class="stat-label">Publishers</span>';
        $html .= '</div>';

        $html .= '<div class="stat-item">';
        $html .= '<strong>' . number_format($stats['languages']) . '</strong>';
        $html .= '<span class="stat-label">Languages</span>';
        $html .= '</div>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Generate logo HTML element
 */
function render_logo($classes = '', $link = true) {
    $logo_classes = "logo-image {$classes}";
    $logo_html = "<img src=\"" . logo_url() . "\" alt=\"" . APP_NAME . " Logo\" class=\"{$logo_classes}\">";

    if ($link) {
        return "<a href=\"" . APP_URL . "\" class=\"logo-link\">{$logo_html}</a>";
    }

    return $logo_html;
}

/**
 * Generate favicon link tags for different browsers
 */
function render_favicon_links() {
    return generate_favicon_tags();
}

/**
 * Check if icon assets are properly loaded
 */
function validate_icon_assets() {
    $status = [
        'icon_exists' => false,
        'logo_exists' => false,
        'manifest_exists' => false,
        'all_ready' => false
    ];

    $icon_path = __DIR__ . '/../assets/images/icon.png';
    $logo_path = __DIR__ . '/../assets/images/logo.png';
    $manifest_path = __DIR__ . '/../assets/manifest.json';

    $status['icon_exists'] = file_exists($icon_path);
    $status['logo_exists'] = file_exists($logo_path);
    $status['manifest_exists'] = file_exists($manifest_path);
    $status['all_ready'] = $status['icon_exists'] && $status['logo_exists'] && $status['manifest_exists'];

    return $status;
}
?>
