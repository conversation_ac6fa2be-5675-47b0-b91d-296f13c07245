<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Contact Page
 */

$page_title = 'Contact Us - ' . APP_NAME;
$page_description = 'Get in touch with मैथिली विकास कोष for inquiries, research assistance, or collaboration opportunities.';

$success_message = '';
$error_message = '';

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = clean_input($_POST['name'] ?? '');
    $email = clean_input($_POST['email'] ?? '', 'email');
    $phone = clean_input($_POST['phone'] ?? '');
    $subject = clean_input($_POST['subject'] ?? '');
    $message = clean_input($_POST['message'] ?? '');
    $inquiry_type = clean_input($_POST['inquiry_type'] ?? '');
    
    // Validate required fields
    $errors = validate_required_fields($_POST, ['name', 'email', 'subject', 'message']);
    
    // Validate email
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    }
    
    if (empty($errors)) {
        try {
            $db = new Database();
            
            // Save contact inquiry to database (optional)
            $inquiry_id = $db->insert('contact_inquiries', [
                'id' => generate_id('inquiry_'),
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'subject' => $subject,
                'message' => $message,
                'inquiryType' => $inquiry_type,
                'status' => 'NEW',
                'ipAddress' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'createdAt' => date('Y-m-d H:i:s')
            ]);
            
            // Log the inquiry
            log_activity("New contact inquiry received from: $email", 'INFO');
            
            $success_message = 'Thank you for your message! We will get back to you soon.';
            
            // Clear form data
            $name = $email = $phone = $subject = $message = $inquiry_type = '';
            
        } catch (Exception $e) {
            log_activity("Contact form error: " . $e->getMessage(), 'ERROR');
            $error_message = 'Sorry, there was an error sending your message. Please try again or contact us directly.';
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

include 'templates/header.php';
?>

<main class="main-content">
    <!-- Hero Section -->
    <section class="hero-section">
        <!-- Floating Icons -->
        <div class="hero-icons">
            <div class="hero-icon">📞</div>
            <div class="hero-icon">✉️</div>
            <div class="hero-icon">🤝</div>
            <div class="hero-icon">💬</div>
        </div>

        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="nepali-text">सम्पर्क</span>
                    <span class="english-text">Contact Us</span>
                </h1>
                <p class="hero-description">
                    We'd love to hear from you. Get in touch for inquiries, research assistance, collaboration opportunities, or any questions about our Maithili literature collection.
                </p>
                <div class="catalog-stats">
                    <?php
                    try {
                        $db = new Database();

                        // Get real statistics using direct SQL queries
                        $contact_books = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE isDeleted = 0") ?: 0;
                        $contact_authors = $db->fetchColumn("SELECT COUNT(*) FROM authors WHERE isDeleted = 0") ?: 0;
                        $contact_categories = $db->fetchColumn("SELECT COUNT(*) FROM categories WHERE isDeleted = 0") ?: 0;
                        $contact_publishers = $db->fetchColumn("SELECT COUNT(*) FROM publishers WHERE isDeleted = 0") ?: 0;
                        $contact_languages = $db->fetchColumn("SELECT COUNT(*) FROM languages WHERE isDeleted = 0") ?: 0;
                    ?>
                        <div class="stat-item">
                            <strong><?= number_format($contact_books) ?></strong>
                            <span>Books Available</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($contact_categories) ?></strong>
                            <span>Categories</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($contact_authors) ?></strong>
                            <span>Authors</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($contact_publishers) ?></strong>
                            <span>Publishers</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($contact_languages) ?></strong>
                            <span>Languages</span>
                        </div>
                    <?php
                    } catch (Exception $e) {
                        // Fallback stats if database is unavailable
                    ?>
                        <div class="stat-item">
                            <strong>24/7</strong>
                            <span>Online Support</span>
                        </div>
                        <div class="stat-item">
                            <strong>Quick</strong>
                            <span>Response Time</span>
                        </div>
                        <div class="stat-item">
                            <strong>Expert</strong>
                            <span>Assistance</span>
                        </div>
                        <div class="stat-item">
                            <strong>Free</strong>
                            <span>Consultation</span>
                        </div>
                        <div class="stat-item">
                            <strong>Reliable</strong>
                            <span>Service</span>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Content -->
    <section class="contact-section">
        <div class="container">
            <div class="contact-grid">
                <!-- Contact Form -->
                <div class="contact-form-container">
                    <h2>
                        <span class="nepali-text">सन्देश पठाउनुहोस्</span>
                        <span class="english-text">Send us a Message</span>
                    </h2>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <?= $success_message ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-error">
                            <?= $error_message ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="contact-form" data-validate>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" id="name" name="name" class="form-input" 
                                       value="<?= htmlspecialchars($name ?? '') ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" id="email" name="email" class="form-input" 
                                       value="<?= htmlspecialchars($email ?? '') ?>" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" id="phone" name="phone" class="form-input" 
                                       value="<?= htmlspecialchars($phone ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label for="inquiry_type" class="form-label">Inquiry Type</label>
                                <select id="inquiry_type" name="inquiry_type" class="form-input">
                                    <option value="">Select Type</option>
                                    <option value="GENERAL" <?= ($inquiry_type ?? '') === 'GENERAL' ? 'selected' : '' ?>>General Inquiry</option>
                                    <option value="RESEARCH" <?= ($inquiry_type ?? '') === 'RESEARCH' ? 'selected' : '' ?>>Research Assistance</option>
                                    <option value="COLLABORATION" <?= ($inquiry_type ?? '') === 'COLLABORATION' ? 'selected' : '' ?>>Collaboration</option>
                                    <option value="DONATION" <?= ($inquiry_type ?? '') === 'DONATION' ? 'selected' : '' ?>>Book Donation</option>
                                    <option value="TECHNICAL" <?= ($inquiry_type ?? '') === 'TECHNICAL' ? 'selected' : '' ?>>Technical Support</option>
                                    <option value="OTHER" <?= ($inquiry_type ?? '') === 'OTHER' ? 'selected' : '' ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject" class="form-label">Subject *</label>
                            <input type="text" id="subject" name="subject" class="form-input" 
                                   value="<?= htmlspecialchars($subject ?? '') ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="message" class="form-label">Message *</label>
                            <textarea id="message" name="message" class="form-textarea" rows="6" required><?= htmlspecialchars($message ?? '') ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <span class="nepali-text">पठाउनुहोस्</span>
                            <span class="english-text">Send Message</span>
                        </button>
                    </form>
                </div>
                
                <!-- Contact Information -->
                <div class="contact-info-container">
                    <h2>
                        <span class="nepali-text">सम्पर्क जानकारी</span>
                        <span class="english-text">Contact Information</span>
                    </h2>
                    
                    <div class="contact-info-cards">
                        <div class="contact-info-card">
                            <div class="contact-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                                </svg>
                            </div>
                            <h3>Address</h3>
                            <p>
                                <span class="nepali-text">मैथिली विकास कोष</span><br>
                                <span class="nepali-text">विद्यापति पुस्तकालय</span><br>
                                Nepal
                            </p>
                        </div>
                        
                        <div class="contact-info-card">
                            <div class="contact-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/>
                                </svg>
                            </div>
                            <h3>Phone</h3>
                            <p>
                                <a href="tel:<?= CONTACT_PHONE ?>"><?= CONTACT_PHONE ?></a>
                            </p>
                        </div>
                        
                        <div class="contact-info-card">
                            <div class="contact-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                </svg>
                            </div>
                            <h3>Email</h3>
                            <p>
                                <a href="mailto:<?= CONTACT_EMAIL ?>"><?= CONTACT_EMAIL ?></a>
                            </p>
                        </div>
                        
                        <div class="contact-info-card">
                            <div class="contact-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                                    <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
                                </svg>
                            </div>
                            <h3>Office Hours</h3>
                            <p>
                                Sunday - Friday<br>
                                10:00 AM - 5:00 PM<br>
                                <small>(Nepal Time)</small>
                            </p>
                        </div>
                    </div>
                    
                    <!-- Developer Info -->
                    <div class="developer-info">
                        <h3>
                            <span class="nepali-text">विकासकर्ता</span>
                            <span class="english-text">Developer</span>
                        </h3>
                        <div class="developer-card">
                            <h4 class="nepali-text"><?= CONTACT_DEVELOPER ?></h4>
                            <p>Lead Developer & Cultural Preservationist</p>
                            <p>📞 <a href="tel:<?= CONTACT_PHONE ?>"><?= CONTACT_PHONE ?></a></p>
                            <p>Specialized in library management systems and cultural heritage digitization</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2>
                <span class="nepali-text">बारम्बार सोधिने प्रश्नहरू</span>
                <span class="english-text">Frequently Asked Questions</span>
            </h2>
            
            <div class="faq-grid">
                <div class="faq-item">
                    <h3>How can I access the book collection?</h3>
                    <p>Our digital catalog is freely accessible through this website. You can browse, search, and view book details online. For physical access to books, please contact us to arrange a visit.</p>
                </div>
                
                <div class="faq-item">
                    <h3>Can I donate books to the library?</h3>
                    <p>Yes! We welcome book donations, especially Maithili literature and cultural materials. Please contact us to discuss your donation and arrange for collection or delivery.</p>
                </div>
                
                <div class="faq-item">
                    <h3>Do you provide research assistance?</h3>
                    <p>Absolutely! Our team can help researchers, students, and scholars find relevant materials and provide guidance on Maithili literature and culture. Contact us with your research needs.</p>
                </div>
                
                <div class="faq-item">
                    <h3>How can I collaborate with the library?</h3>
                    <p>We're always open to collaborations with educational institutions, researchers, and cultural organizations. Please reach out to discuss potential partnership opportunities.</p>
                </div>
            </div>
        </div>
    </section>
</main>

<style>
.contact-section {
    padding: 4rem 0;
    background: var(--white);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.contact-form-container,
.contact-info-container {
    background: var(--secondary-color);
    border-radius: var(--radius-xl);
    padding: 2rem;
    border: 1px solid var(--medium-gray);
}

.contact-form-container h2,
.contact-info-container h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.contact-form-container h2 .nepali-text,
.contact-info-container h2 .nepali-text {
    display: block;
    margin-bottom: 0.25rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 500;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: all var(--transition-fast);
    background: var(--white);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(85, 8, 12, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-info-cards {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.contact-info-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--medium-gray);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    color: var(--primary-color);
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.contact-info-card h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
}

.contact-info-card p {
    color: var(--text-light);
    margin: 0;
}

.contact-info-card a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-info-card a:hover {
    text-decoration: underline;
}

.developer-info {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
}

.developer-info h3 {
    color: var(--white);
    margin-bottom: 1rem;
}

.developer-info h3 .nepali-text {
    display: block;
    margin-bottom: 0.25rem;
}

.developer-card h4 {
    color: var(--light-accent);
    margin-bottom: 0.5rem;
}

.developer-card p {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.developer-card a {
    color: var(--light-accent);
    text-decoration: none;
}

.developer-card a:hover {
    text-decoration: underline;
}

.faq-section {
    background: var(--secondary-color);
    padding: 4rem 0;
}

.faq-section h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 3rem;
}

.faq-section h2 .nepali-text {
    display: block;
    margin-bottom: 0.5rem;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--medium-gray);
}

.faq-item h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.faq-item p {
    color: var(--text-light);
    margin: 0;
}

.alert {
    padding: 0.75rem;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.alert-error {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .faq-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include 'templates/footer.php'; ?>
