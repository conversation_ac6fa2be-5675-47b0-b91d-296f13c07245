<?php
/**
 * मैथिली विकास कोष - Contact Inquiries Management
 * View and manage contact form submissions
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

$db = new Database();

// Handle status updates
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    $inquiry_id = clean_input($_POST['inquiry_id']);
    $new_status = clean_input($_POST['status']);

    try {
        $db->update('contact_inquiries',
            ['status' => $new_status, 'updatedAt' => date('Y-m-d H:i:s')],
            'id = ?',
            [$inquiry_id]
        );
        $success_message = "Inquiry status updated successfully.";
        log_activity("Updated contact inquiry status: $inquiry_id to $new_status", 'INFO');
    } catch (Exception $e) {
        $error_message = "Failed to update inquiry status: " . $e->getMessage();
    }
}

// Handle delete action
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'delete_inquiry') {
    $inquiry_id = clean_input($_POST['inquiry_id']);

    try {
        $db->delete('contact_inquiries', 'id = ?', [$inquiry_id]);
        $success_message = "Inquiry deleted successfully.";
        log_activity("Deleted contact inquiry: $inquiry_id", 'INFO');
    } catch (Exception $e) {
        $error_message = "Failed to delete inquiry: " . $e->getMessage();
    }
}

// Get filter parameters
$status_filter = clean_input($_GET['status'] ?? '');
$search_query = clean_input($_GET['search'] ?? '');

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($search_query)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
    $search_param = "%$search_query%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get pagination parameters
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 15;
$offset = ($page - 1) * $limit;

try {
    // Get inquiries with filters
    $inquiries = $db->fetchAll("
        SELECT * FROM contact_inquiries
        $where_clause
        ORDER BY createdAt DESC
        LIMIT $limit OFFSET $offset
    ", $params);

    // Get total count for pagination
    $total_inquiries = $db->fetchColumn("SELECT COUNT(*) FROM contact_inquiries $where_clause", $params);
    $total_pages = ceil($total_inquiries / $limit);

    // Get statistics
    $stats = [
        'total' => $db->fetchColumn("SELECT COUNT(*) FROM contact_inquiries"),
        'new' => $db->fetchColumn("SELECT COUNT(*) FROM contact_inquiries WHERE status = 'NEW'"),
        'in_progress' => $db->fetchColumn("SELECT COUNT(*) FROM contact_inquiries WHERE status = 'IN_PROGRESS'"),
        'resolved' => $db->fetchColumn("SELECT COUNT(*) FROM contact_inquiries WHERE status = 'RESOLVED'"),
        'closed' => $db->fetchColumn("SELECT COUNT(*) FROM contact_inquiries WHERE status = 'CLOSED'")
    ];

} catch (Exception $e) {
    $error_message = "Failed to load contact inquiries: " . $e->getMessage();
    $inquiries = [];
    $total_inquiries = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'new' => 0, 'in_progress' => 0, 'resolved' => 0, 'closed' => 0];
}

$page_title = 'Contact Inquiries - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">

    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
?>

    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="contact_inquiries.php" class="nav-link active">
                            <span class="nav-icon">💬</span>
                            Contact Inquiries
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="../database_reset.php" class="nav-link" style="color: #ef4444;">
                            <span class="nav-icon">🗑️</span>
                            Database Reset
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">💬</span>
                            Contact Inquiries
                        </h1>
                        <p class="header-subtitle">Manage contact form submissions</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-item active">Contact Inquiries</span>
                </nav>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">💬</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['total']) ?></div>
                            <div class="stat-label">Total Inquiries</div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">🆕</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['new']) ?></div>
                            <div class="stat-label">New Inquiries</div>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['in_progress']) ?></div>
                            <div class="stat-label">In Progress</div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['resolved']) ?></div>
                            <div class="stat-label">Resolved</div>
                        </div>
                    </div>

                    <div class="stat-card secondary">
                        <div class="stat-icon">🔒</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['closed']) ?></div>
                            <div class="stat-label">Closed</div>
                        </div>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success">
                        <span style="margin-right: 0.5rem;">✅</span>
                        <?= htmlspecialchars($success_message) ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger">
                        <span style="margin-right: 0.5rem;">❌</span>
                        <?= htmlspecialchars($error_message) ?>
                    </div>
                <?php endif; ?>

                <!-- Filters and Search -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span style="margin-right: 0.5rem;">🔍</span>
                            Search & Filter Inquiries
                        </h3>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="filter-form">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
                                <div class="form-group">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" id="search" name="search" class="form-control"
                                           placeholder="Search by name, email, subject..."
                                           value="<?= htmlspecialchars($search_query) ?>">
                                </div>

                                <div class="form-group">
                                    <label for="status" class="form-label">Status</label>
                                    <select id="status" name="status" class="form-control">
                                        <option value="">All Statuses</option>
                                        <option value="NEW" <?= $status_filter === 'NEW' ? 'selected' : '' ?>>New</option>
                                        <option value="IN_PROGRESS" <?= $status_filter === 'IN_PROGRESS' ? 'selected' : '' ?>>In Progress</option>
                                        <option value="RESOLVED" <?= $status_filter === 'RESOLVED' ? 'selected' : '' ?>>Resolved</option>
                                        <option value="CLOSED" <?= $status_filter === 'CLOSED' ? 'selected' : '' ?>>Closed</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <span style="margin-right: 0.5rem;">🔍</span>
                                        Search
                                    </button>
                                    <a href="contact_inquiries.php" class="btn btn-secondary" style="margin-left: 0.5rem;">
                                        <span style="margin-right: 0.5rem;">🔄</span>
                                        Reset
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Inquiries Table -->
                <div class="card" style="margin-top: 1.5rem;">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span style="margin-right: 0.5rem;">📋</span>
                            Contact Inquiries
                            <?php if ($total_inquiries > 0): ?>
                                <span style="font-weight: normal; color: var(--text-light);">
                                    (<?= number_format($total_inquiries) ?> total)
                                </span>
                            <?php endif; ?>
                        </h3>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <?php if (empty($inquiries)): ?>
                            <div style="padding: 3rem; text-align: center; color: var(--text-light);">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">📭</div>
                                <h3 style="margin-bottom: 0.5rem;">No Contact Inquiries Found</h3>
                                <p>
                                    <?php if (!empty($search_query) || !empty($status_filter)): ?>
                                        No inquiries match your search criteria. Try adjusting your filters.
                                    <?php else: ?>
                                        No contact inquiries have been submitted yet.
                                    <?php endif; ?>
                                </p>
                                <a href="../index.php?page=contact" class="btn btn-primary" style="margin-top: 1rem;">
                                    <span style="margin-right: 0.5rem;">📝</span>
                                    Test Contact Form
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Contact Info</th>
                                            <th>Subject & Message</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($inquiries as $inquiry): ?>
                                            <tr>
                                                <td>
                                                    <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                                                        <strong style="color: var(--text-dark);">
                                                            <?= htmlspecialchars($inquiry['name']) ?>
                                                        </strong>
                                                        <a href="mailto:<?= htmlspecialchars($inquiry['email']) ?>"
                                                           style="color: var(--primary-color); text-decoration: none; font-size: 0.875rem;">
                                                            📧 <?= htmlspecialchars($inquiry['email']) ?>
                                                        </a>
                                                        <?php if ($inquiry['phone']): ?>
                                                            <span style="color: var(--text-light); font-size: 0.875rem;">
                                                                📞 <?= htmlspecialchars($inquiry['phone']) ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                                                        <strong style="color: var(--text-dark); font-size: 0.9rem;">
                                                            <?= htmlspecialchars($inquiry['subject']) ?>
                                                        </strong>
                                                        <span style="color: var(--text-light); font-size: 0.875rem; line-height: 1.4;">
                                                            <?= htmlspecialchars(substr($inquiry['message'], 0, 120)) ?>
                                                            <?= strlen($inquiry['message']) > 120 ? '...' : '' ?>
                                                        </span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($inquiry['inquiryType']): ?>
                                                        <span class="badge badge-<?= strtolower($inquiry['inquiryType']) ?>">
                                                            <?= htmlspecialchars($inquiry['inquiryType']) ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span style="color: var(--text-light); font-style: italic;">General</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="inquiry_id" value="<?= $inquiry['id'] ?>">
                                                        <select name="status" onchange="this.form.submit()" class="form-control" style="font-size: 0.875rem; padding: 0.375rem;">
                                                            <option value="NEW" <?= $inquiry['status'] === 'NEW' ? 'selected' : '' ?>>🆕 New</option>
                                                            <option value="IN_PROGRESS" <?= $inquiry['status'] === 'IN_PROGRESS' ? 'selected' : '' ?>>⏳ In Progress</option>
                                                            <option value="RESOLVED" <?= $inquiry['status'] === 'RESOLVED' ? 'selected' : '' ?>>✅ Resolved</option>
                                                            <option value="CLOSED" <?= $inquiry['status'] === 'CLOSED' ? 'selected' : '' ?>>🔒 Closed</option>
                                                        </select>
                                                    </form>
                                                </td>
                                                <td>
                                                    <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                                                        <span style="color: var(--text-dark); font-size: 0.875rem;">
                                                            <?= date('M j, Y', strtotime($inquiry['createdAt'])) ?>
                                                        </span>
                                                        <span style="color: var(--text-light); font-size: 0.75rem;">
                                                            <?= date('g:i A', strtotime($inquiry['createdAt'])) ?>
                                                        </span>
                                                        <?php if ($inquiry['ipAddress']): ?>
                                                            <span style="color: var(--text-light); font-size: 0.75rem;">
                                                                🌐 <?= htmlspecialchars($inquiry['ipAddress']) ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div style="display: flex; gap: 0.5rem; align-items: center;">
                                                        <button onclick="viewInquiry('<?= htmlspecialchars($inquiry['id']) ?>', '<?= htmlspecialchars(addslashes($inquiry['name'])) ?>', '<?= htmlspecialchars(addslashes($inquiry['email'])) ?>', '<?= htmlspecialchars(addslashes($inquiry['subject'])) ?>', '<?= htmlspecialchars(addslashes($inquiry['message'])) ?>')"
                                                                class="btn btn-primary" style="font-size: 0.75rem; padding: 0.375rem 0.75rem;">
                                                            👁️ View
                                                        </button>
                                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this inquiry?')">
                                                            <input type="hidden" name="action" value="delete_inquiry">
                                                            <input type="hidden" name="inquiry_id" value="<?= $inquiry['id'] ?>">
                                                            <button type="submit" class="btn btn-danger" style="font-size: 0.75rem; padding: 0.375rem 0.75rem;">
                                                                🗑️ Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="card" style="margin-top: 1.5rem;">
                        <div class="card-body" style="padding: 1rem;">
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    Showing <?= number_format(($page - 1) * $limit + 1) ?> to
                                    <?= number_format(min($page * $limit, $total_inquiries)) ?> of
                                    <?= number_format($total_inquiries) ?> inquiries
                                </div>
                                <div class="pagination">
                                    <?php if ($page > 1): ?>
                                        <a href="?page=<?= $page - 1 ?><?= !empty($search_query) ? '&search=' . urlencode($search_query) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>"
                                           class="btn btn-secondary btn-sm">
                                            ← Previous
                                        </a>
                                    <?php endif; ?>

                                    <?php
                                    $start_page = max(1, $page - 2);
                                    $end_page = min($total_pages, $page + 2);

                                    if ($start_page > 1): ?>
                                        <a href="?page=1<?= !empty($search_query) ? '&search=' . urlencode($search_query) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>"
                                           class="btn btn-secondary btn-sm">1</a>
                                        <?php if ($start_page > 2): ?>
                                            <span style="padding: 0.5rem;">...</span>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                        <a href="?page=<?= $i ?><?= !empty($search_query) ? '&search=' . urlencode($search_query) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>"
                                           class="btn <?= $i === $page ? 'btn-primary' : 'btn-secondary' ?> btn-sm">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($end_page < $total_pages): ?>
                                        <?php if ($end_page < $total_pages - 1): ?>
                                            <span style="padding: 0.5rem;">...</span>
                                        <?php endif; ?>
                                        <a href="?page=<?= $total_pages ?><?= !empty($search_query) ? '&search=' . urlencode($search_query) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>"
                                           class="btn btn-secondary btn-sm"><?= $total_pages ?></a>
                                    <?php endif; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <a href="?page=<?= $page + 1 ?><?= !empty($search_query) ? '&search=' . urlencode($search_query) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>"
                                           class="btn btn-secondary btn-sm">
                                            Next →
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Inquiry Detail Modal -->
    <div id="inquiryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 style="margin: 0;">Inquiry Details</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="inquiryDetails"></div>
            </div>
        </div>
    </div>

    <!-- Custom Styles -->
    <style>
        /* Alert Styles */
        .alert {
            padding: 1rem 1.5rem;
            border-radius: var(--radius-md);
            margin-bottom: 1.5rem;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
        }

        .alert-success {
            background: #dcfce7;
            border-color: #bbf7d0;
            color: #16a34a;
        }

        .alert-danger {
            background: #fee2e2;
            border-color: #fecaca;
            color: #dc2626;
        }

        /* Badge Styles */
        .badge {
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .badge-general { background: #f3f4f6; color: #374151; }
        .badge-research { background: #dbeafe; color: #1e40af; }
        .badge-collaboration { background: #d1fae5; color: #065f46; }
        .badge-donation { background: #fef3c7; color: #92400e; }
        .badge-technical { background: #fce7f3; color: #be185d; }
        .badge-other { background: #e5e7eb; color: #6b7280; }

        /* Filter Form */
        .filter-form .form-group {
            margin-bottom: 0;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .pagination {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .pagination-info {
            color: var(--text-light);
            font-size: 0.875rem;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }

        .modal-content {
            background-color: var(--white);
            margin: 5% auto;
            border-radius: var(--radius-lg);
            width: 90%;
            max-width: 700px;
            position: relative;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--admin-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-light);
            padding: 0.5rem;
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .close:hover {
            color: var(--primary-color);
            background: var(--secondary-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .pagination-container {
                flex-direction: column;
                text-align: center;
            }

            .filter-form > div {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 2% auto;
            }
        }
    </style>

    <!-- JavaScript -->
    <script src="<?= asset_url('js/admin.js') ?>"></script>
    <script>
        // Inquiry Modal Functions
        function viewInquiry(inquiryId, name, email, subject, message) {
            const modal = document.getElementById('inquiryModal');
            const detailsContainer = document.getElementById('inquiryDetails');

            detailsContainer.innerHTML = `
                <div style="display: grid; gap: 1.5rem;">
                    <div>
                        <h4 style="margin: 0 0 0.5rem 0; color: var(--primary-color);">Contact Information</h4>
                        <div style="background: var(--light-gray); padding: 1rem; border-radius: var(--radius-md);">
                            <p style="margin: 0 0 0.5rem 0;"><strong>Name:</strong> ${name}</p>
                            <p style="margin: 0;"><strong>Email:</strong> <a href="mailto:${email}" style="color: var(--primary-color);">${email}</a></p>
                        </div>
                    </div>

                    <div>
                        <h4 style="margin: 0 0 0.5rem 0; color: var(--primary-color);">Subject</h4>
                        <div style="background: var(--light-gray); padding: 1rem; border-radius: var(--radius-md);">
                            <p style="margin: 0; font-weight: 500;">${subject}</p>
                        </div>
                    </div>

                    <div>
                        <h4 style="margin: 0 0 0.5rem 0; color: var(--primary-color);">Message</h4>
                        <div style="background: var(--light-gray); padding: 1rem; border-radius: var(--radius-md); max-height: 200px; overflow-y: auto;">
                            <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${message}</p>
                        </div>
                    </div>

                    <div style="display: flex; gap: 1rem; justify-content: flex-end; padding-top: 1rem; border-top: 1px solid var(--admin-border);">
                        <a href="mailto:${email}?subject=Re: ${encodeURIComponent(subject)}"
                           class="btn btn-primary">
                            📧 Reply via Email
                        </a>
                        <button onclick="closeModal()" class="btn btn-secondary">
                            Close
                        </button>
                    </div>
                </div>
            `;

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('inquiryModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('inquiryModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // Auto-submit search form on Enter
        document.getElementById('search').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                this.form.submit();
            }
        });

        // Show loading state for status updates
        document.querySelectorAll('select[name="status"]').forEach(select => {
            select.addEventListener('change', function() {
                this.style.opacity = '0.6';
                this.style.pointerEvents = 'none';

                // Create loading indicator
                const loading = document.createElement('span');
                loading.innerHTML = ' ⏳';
                loading.style.fontSize = '0.875rem';
                this.parentNode.appendChild(loading);
            });
        });

        // Auto-refresh page every 5 minutes to show new inquiries
        setTimeout(function() {
            if (document.visibilityState === 'visible') {
                window.location.reload();
            }
        }, 300000); // 5 minutes
    </script>
</body>
</html>
