/* Import Inter Font for English Text */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Main Stylesheet with Custom Color Scheme
 *
 * Color Palette:
 * Primary Color: #55080C (Deep burgundy red)
 * Secondary Color: #FBF7F8 (Very light pink/off-white)
 * Accent Color: #8B1538 (Medium burgundy red)
 * Light Accent: #D4A574 (Warm gold/beige)
 * Text Dark: #2C1810 (Dark brown)
 * Text Light: #6B4E3D (Medium brown)
 * Success Color: #2D5016 (Dark green)
 * Warning Color: #B8860B (Dark goldenrod)
 */

/* Local Devanagari Font Declarations */
@font-face {
    font-family: 'Noto Sans Devanagari';
    src: url('../fonts/NotoSansDevanagari-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans Devanagari';
    src: url('../fonts/NotoSansDevanagari-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* CSS Variables for Color Scheme */
:root {
    --primary-color: #55080C;
    --secondary-color: #FBF7F8;
    --accent-color: #8B1538;
    --light-accent: #D4A574;
    --text-dark: #2C1810;
    --text-light: #6B4E3D;
    --success-color: #2D5016;
    --warning-color: #B8860B;
    --error-color: #DC2626;
    --white: #FFFFFF;
    --light-gray: #F5F5F5;
    --medium-gray: #E5E5E5;
    --dark-gray: #666666;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(85, 8, 12, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(85, 8, 12, 0.1), 0 2px 4px -1px rgba(85, 8, 12, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(85, 8, 12, 0.1), 0 4px 6px -2px rgba(85, 8, 12, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(85, 8, 12, 0.1), 0 10px 10px -5px rgba(85, 8, 12, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--secondary-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
.nepali-text, .maithili-text, .devanagari-text {
    font-family: 'Noto Sans Devanagari', 'Mangal', 'Nirmala UI', sans-serif;
    font-weight: 500;
    line-height: 1.8; /* Better line spacing for Devanagari */
    letter-spacing: 0.02em; /* Slight letter spacing for readability */
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: "liga", "kern";
    font-feature-settings: "liga", "kern";
}

.english-text {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

/* Mixed language content */
.mixed-text {
    font-family: 'Noto Sans Devanagari', 'Inter', sans-serif;
    line-height: 1.7;
}

/* Devanagari-specific typography improvements */
.devanagari-title {
    font-family: 'Noto Sans Devanagari', sans-serif;
    font-weight: 700;
    line-height: 1.4;
    letter-spacing: 0.01em;
}

.devanagari-body {
    font-family: 'Noto Sans Devanagari', sans-serif;
    font-weight: 400;
    line-height: 1.8;
    letter-spacing: 0.02em;
}

/* Icon Utility Classes */
.icon {
    display: inline-block;
    vertical-align: middle;
}

.icon-small {
    width: 16px;
    height: 16px;
}

.icon-medium {
    width: 24px;
    height: 24px;
}

.icon-large {
    width: 32px;
    height: 32px;
}

.icon-xlarge {
    width: 48px;
    height: 48px;
}

.icon-xxlarge {
    width: 64px;
    height: 64px;
}

.app-icon {
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.favicon {
    width: 16px;
    height: 16px;
}

.touch-icon {
    width: 180px;
    height: 180px;
    border-radius: var(--radius-xl);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-color);
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.main-content {
    min-height: 100vh;
    padding-top: 0; /* Remove extra padding since body already has padding-top */
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white);
    padding: 0.5rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    height: 70px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 1rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: var(--light-accent);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-color);
    overflow: hidden;
    flex-shrink: 0;
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: var(--radius-lg);
}

.logo-text h1 {
    font-size: 1.25rem;
    margin: 0;
    color: var(--white);
    line-height: 1.2;
}

.logo-text p {
    font-size: 0.75rem;
    margin: 0;
    opacity: 0.9;
    color: var(--white);
    line-height: 1.2;
}

.nav {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1rem;
    align-items: center;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--white);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    transform: translateY(-1px);
}

.nav-link.active {
    background: var(--light-accent);
    color: var(--primary-color);
}

.nav-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    transition: transform var(--transition-fast);
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

/* Header scroll effect */
.header.scrolled {
    box-shadow: 0 4px 20px rgba(85, 8, 12, 0.15);
    backdrop-filter: blur(10px);
}

/* Body spacing for fixed header */
body {
    padding-top: 70px;
}

/* Catalog Header */
.catalog-header {
    background: linear-gradient(135deg, #2c0a0e 0%, var(--primary-color) 30%, #8B1538 70%, #a91d42 100%);
    color: var(--white);
    padding: 4rem 0 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 450px;
    display: flex;
    align-items: center;
}

.catalog-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.2) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="catalog" width="45" height="45" patternUnits="userSpaceOnUse"><rect width="12" height="16" x="6" y="14" fill="rgba(0,0,0,0.08)" rx="1"/><rect width="12" height="16" x="20" y="12" fill="rgba(0,0,0,0.06)" rx="1"/><rect width="12" height="16" x="34" y="16" fill="rgba(0,0,0,0.07)" rx="1"/></pattern></defs><rect width="100" height="100" fill="url(%23catalog)"/></svg>');
    opacity: 0.4;
}

.catalog-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
}

.catalog-header-content {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 2rem;
    animation: fadeInUp 1s ease-out;
}

.catalog-title {
    margin-bottom: 2rem;
    line-height: 1.2;
    position: relative;
}

.catalog-title .nepali-text {
    display: block;
    font-family: var(--devanagari-font);
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #ffffff;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 0 20px rgba(255, 255, 255, 0.3);
    position: relative;
}

.catalog-title .nepali-text::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ffffff, transparent);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.catalog-title .english-text {
    display: block;
    font-size: 1.75rem;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
}

.catalog-description {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: #ffffff;
    line-height: 1.7;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
    animation: fadeInUp 1s ease-out 0.3s both;
}

.catalog-stats {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.catalog-stats .stat-item {
    text-align: center;
    padding: 1rem 1.25rem;
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 110px;
    flex: 0 0 auto;
}

.catalog-stats .stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.catalog-stats .stat-item:hover::before {
    left: 100%;
}

.catalog-stats .stat-item:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.5);
}

.catalog-stats .stat-item strong {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.catalog-stats .stat-item span {
    font-size: 0.9rem;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Catalog Icons */
.catalog-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.catalog-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.15);
    font-size: 2.5rem;
    animation: float 7s ease-in-out infinite;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
}

.catalog-icon:nth-child(1) {
    top: 18%;
    left: 10%;
    animation-delay: 0s;
}

.catalog-icon:nth-child(2) {
    top: 30%;
    right: 12%;
    animation-delay: 1.5s;
}

.catalog-icon:nth-child(3) {
    bottom: 35%;
    left: 15%;
    animation-delay: 3s;
}

.catalog-icon:nth-child(4) {
    bottom: 25%;
    right: 10%;
    animation-delay: 4.5s;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #2c0a0e 0%, var(--primary-color) 30%, #8B1538 70%, #a91d42 100%);
    color: var(--white);
    padding: 4.5rem 0 3.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.2) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="library" width="50" height="50" patternUnits="userSpaceOnUse"><rect width="10" height="15" x="8" y="17" fill="rgba(0,0,0,0.08)" rx="1"/><rect width="10" height="15" x="20" y="15" fill="rgba(0,0,0,0.06)" rx="1"/><rect width="10" height="15" x="32" y="18" fill="rgba(0,0,0,0.07)" rx="1"/></pattern></defs><rect width="100" height="100" fill="url(%23library)"/></svg>');
    opacity: 0.4;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
}

.hero-content {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 2rem;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    margin-bottom: 2rem;
    line-height: 1.2;
    position: relative;
}

.hero-title .nepali-text {
    display: block;
    font-family: var(--devanagari-font);
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 0 20px rgba(255, 255, 255, 0.3);
    position: relative;
}

.hero-title .nepali-text::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, transparent, #ffffff, transparent);
    border-radius: 2px;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.hero-title .english-text {
    display: block;
    font-size: 2rem;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
}

.hero-subtitle {
    margin-bottom: 2.5rem;
    line-height: 1.4;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-subtitle .nepali-text {
    display: block;
    font-family: var(--devanagari-font);
    font-size: 1.75rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
}

.hero-subtitle .english-text {
    display: block;
    font-size: 1.25rem;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.hero-description {
    font-size: 1.375rem;
    color: #ffffff;
    max-width: 750px;
    margin: 0 auto 3rem;
    line-height: 1.7;
    font-weight: 300;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 1.2s both;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-stats .stat-item {
    text-align: center;
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.hero-stats .stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.hero-stats .stat-item:hover::before {
    left: 100%;
}

.hero-stats .stat-item:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.5);
}

.hero-stats .stat-item strong {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-stats .stat-item .stat-label {
    font-size: 0.9rem;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Hero Icons */
.hero-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.hero-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.15);
    font-size: 2.5rem;
    animation: float 8s ease-in-out infinite;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
}

.hero-icon:nth-child(1) {
    top: 15%;
    left: 8%;
    animation-delay: 0s;
}

.hero-icon:nth-child(2) {
    top: 25%;
    right: 12%;
    animation-delay: 2s;
}

.hero-icon:nth-child(3) {
    bottom: 35%;
    left: 15%;
    animation-delay: 4s;
}

.hero-icon:nth-child(4) {
    bottom: 20%;
    right: 8%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.15;
    }
    25% {
        transform: translateY(-15px) rotate(2deg);
        opacity: 0.25;
    }
    50% {
        transform: translateY(-25px) rotate(-2deg);
        opacity: 0.3;
    }
    75% {
        transform: translateY(-15px) rotate(1deg);
        opacity: 0.25;
    }
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2.5rem;
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.125rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    min-width: 160px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    border-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: #ffffff;
    color: var(--primary-color);
    border-color: #ffffff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
    background: rgba(0, 0, 0, 0.3);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(0, 0, 0, 0.5);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Quick Search Section */
.quick-search-section {
    background: linear-gradient(135deg, var(--white) 0%, var(--secondary-color) 100%);
    padding: 1.5rem 0;
    box-shadow: var(--shadow-lg);
    border-top: 1px solid var(--medium-gray);
    border-bottom: 1px solid var(--medium-gray);
    position: relative;
}

.quick-search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23550808" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.quick-search-form {
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.quick-search-input-group {
    display: flex;
    gap: 0.75rem;
    align-items: stretch;
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 0.625rem;
    box-shadow: var(--shadow-xl);
    border: 2px solid var(--medium-gray);
    transition: all var(--transition-normal);
}

.quick-search-input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(85, 8, 12, 0.1), var(--shadow-xl);
    transform: translateY(-2px);
}

.quick-search-input {
    flex: 1;
    padding: 1.125rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.125rem;
    background: transparent;
    color: var(--text-dark);
    transition: all var(--transition-fast);
}

.quick-search-input:focus {
    outline: none;
}

.quick-search-input::placeholder {
    color: var(--text-light);
    font-weight: 400;
}

.quick-search-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white);
    border: none;
    padding: 1.125rem 2.25rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.625rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.quick-search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-normal);
}

.quick-search-btn:hover::before {
    left: 100%;
}

.quick-search-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.search-icon {
    width: 18px;
    height: 18px;
    transition: transform var(--transition-fast);
}

.quick-search-btn:hover .search-icon {
    transform: scale(1.1) rotate(15deg);
}

/* Statistics Section */
.stats-section {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--white) 100%);
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23550808" stroke-width="0.5" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.stat-card {
    background: var(--white);
    padding: 2.5rem 1.5rem;
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    border: 2px solid var(--light-gray);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--light-accent));
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
}

.stat-card:hover::before {
    transform: translateX(0);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 1.25rem;
    display: block;
    transition: transform var(--transition-normal);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    line-height: 1;
}

.stat-label {
    color: var(--text-light);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Featured Books Section */
.featured-books-section {
    padding: 2.5rem 0;
    background: var(--white);
}

.section-description {
    text-align: center;
    color: var(--text-light);
    font-size: 1.125rem;
    margin-bottom: 3rem;
}

.section-footer {
    text-align: center;
    margin-top: 3rem;
}

/* Search Section */
.search-section {
    background: var(--white);
    padding: 2rem 0;
    box-shadow: var(--shadow-md);
}

.search-form {
    max-width: 1000px;
    margin: 0 auto;
}

.search-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    align-items: end;
}

.search-input-group {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: all var(--transition-fast);
    background: var(--white);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(85, 8, 12, 0.1);
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.search-btn:hover {
    background: var(--accent-color);
}

.search-icon {
    width: 1.25rem;
    height: 1.25rem;
}

.filter-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(85, 8, 12, 0.1);
}

/* Books Section */
.books-section {
    padding: 3rem 0;
}

.section-header {
    margin-bottom: 2rem;
    text-align: center;
}

.section-title {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Professional Books Grid */
.books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    padding: 0 1rem;
}

/* Professional Book Card Design */
.book-card {
    background: var(--white);
    border-radius: 16px;
    overflow: hidden;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.08);
    height: fit-content;
    display: flex;
    flex-direction: column;
    position: relative;
    backdrop-filter: blur(10px);
}

.book-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(85, 8, 12, 0.1);
    border-color: rgba(85, 8, 12, 0.2);
}

/* Professional Book Image Container */
.book-image {
    width: 100%;
    height: 320px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.book-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(0, 0, 0, 0.05) 100%
    );
    pointer-events: none;
    z-index: 1;
}

.book-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(1.02) contrast(1.05);
}

.book-card:hover .book-image img {
    transform: scale(1.05);
    filter: brightness(1.05) contrast(1.1);
}

/* Professional Book Placeholder */
.book-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #cbd5e1 100%);
    color: #64748b;
    position: relative;
}

.book-placeholder::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    right: 20%;
    bottom: 20%;
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
}

.book-icon {
    width: 3.5rem;
    height: 3.5rem;
    opacity: 0.6;
    margin-bottom: 0.5rem;
    color: #94a3b8;
}

.book-placeholder::after {
    content: 'No Cover';
    font-size: 0.75rem;
    font-weight: 500;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Professional Loading State */
.book-card.loading {
    pointer-events: none;
}

.book-card.loading .book-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Professional Focus States */
.book-card:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.book-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}



/* Professional Book Info Section */
.book-info {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
}

/* Professional Book Title */
.book-title {
    font-size: 1.25rem;
    margin: 0;
    color: #1e293b;
    line-height: 1.4;
    font-weight: 700;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 2.8rem;
    font-family: var(--devanagari-font), -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -0.01em;
}

/* Professional Book Details */
.book-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.book-detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    color: #475569;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
}

.book-detail-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.book-detail-item:hover {
    color: #334155;
    background: rgba(85, 8, 12, 0.02);
    margin: 0 -0.5rem;
    padding: 0.5rem;
    border-radius: 8px;
    border-bottom: 1px solid transparent;
}

.detail-icon {
    width: 18px;
    height: 18px;
    color: var(--primary-color);
    flex-shrink: 0;
    opacity: 0.8;
}

.book-author {
    font-weight: 600;
    color: #334155;
    font-family: var(--devanagari-font), sans-serif;
}

.book-category {
    color: var(--accent-color);
    font-weight: 600;
}

/* Professional Book Meta Information */
.book-meta-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.book-meta-item {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    font-size: 0.8rem;
    color: #475569;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.book-meta-item:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.book-year {
    color: var(--primary-color);
}

.book-pages {
    color: var(--accent-color);
}

.book-location {
    color: #f59e0b;
    font-weight: 700;
}

/* Professional Book Action Button */
.book-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, #8B1538 100%);
    color: var(--white);
    padding: 0.875rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    margin-top: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.book-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.book-link:hover::before {
    left: 100%;
}

.book-link:hover {
    background: linear-gradient(135deg, #8B1538 0%, #a91d42 100%);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.link-icon {
    width: 18px;
    height: 18px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.book-link:hover .link-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-md);
    color: var(--text-dark);
    text-decoration: none;
    transition: all var(--transition-fast);
    background: var(--white);
}

.pagination-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.pagination-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-results h4 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.no-results p {
    color: var(--text-light);
    margin-bottom: 2rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--light-accent);
    color: var(--text-dark);
}

.btn-secondary:hover {
    background: var(--warning-color);
    color: var(--white);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #1a3009;
    color: var(--white);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--text-dark) 0%, var(--primary-color) 100%);
    color: var(--white);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: var(--light-accent);
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.footer-section a:hover {
    color: var(--light-accent);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Medium Screen Adjustments */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        max-width: 700px;
    }

    .stat-card {
        padding: 2rem 1.25rem;
    }
}

/* Professional Responsive Design */
@media (max-width: 1200px) {
    .books-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .search-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .books-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
        padding: 0 0.5rem;
    }

    .book-card {
        border-radius: 12px;
    }

    .book-image {
        height: 280px;
    }

    .book-info {
        padding: 1.25rem;
        gap: 0.75rem;
    }

    .book-title {
        font-size: 1.125rem;
        min-height: 2.5rem;
    }

    .book-meta-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .book-meta-item {
        justify-content: center;
        padding: 0.5rem 1rem;
    }

    /* Hero Section Mobile */
    .hero-section {
        padding: 3.5rem 0 2.5rem;
        min-height: 400px;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .hero-title .nepali-text {
        font-size: 3rem;
        margin-bottom: 0.75rem;
    }

    .hero-title .english-text {
        font-size: 1.5rem;
    }

    .hero-subtitle .nepali-text {
        font-size: 1.375rem;
    }

    .hero-subtitle .english-text {
        font-size: 1.125rem;
    }

    .hero-description {
        font-size: 1.125rem;
        margin-bottom: 2.5rem;
        line-height: 1.6;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 280px;
    }

    .hero-icon {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: row;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats .stat-item {
        padding: 0.75rem 1rem;
        min-width: 140px;
        flex: 0 0 auto;
    }

    .hero-stats .stat-item strong {
        font-size: 1.25rem;
    }

    .hero-stats .stat-item .stat-label {
        font-size: 0.85rem;
    }

    /* Quick Search Mobile */
    .quick-search-section {
        padding: 2rem 0;
    }

    .quick-search-input-group {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .quick-search-input {
        padding: 1rem 1.25rem;
        font-size: 1rem;
    }

    .quick-search-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem 1.5rem;
    }

    /* Stats Mobile */
    .stats-section {
        padding: 1.5rem 0;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
        max-width: 100%;
    }

    .stat-card {
        padding: 1.5rem 1rem;
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2.25rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .nav-menu {
        display: none; /* Will be replaced with mobile menu */
    }

    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .books-grid {
        grid-template-columns: 1fr;
        gap: 1.25rem;
        padding: 0;
    }

    .book-card {
        margin: 0 0.5rem;
        border-radius: 16px;
    }

    .book-image {
        height: 240px;
    }

    .book-info {
        padding: 1.25rem;
        gap: 0.75rem;
    }

    .book-title {
        font-size: 1.125rem;
        min-height: 2.25rem;
    }

    .book-detail-item {
        font-size: 0.9rem;
        padding: 0.375rem 0;
    }

    .book-link {
        padding: 1rem 1.25rem;
        font-size: 0.95rem;
    }

    .book-meta-item {
        font-size: 0.8rem;
        padding: 0.5rem 0.875rem;
    }
}

    .book-meta-item {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    /* Hero Section Small Mobile */
    .hero-section {
        padding: 3rem 0 2rem;
        min-height: 350px;
    }

    .hero-title .nepali-text {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }

    .hero-title .english-text {
        font-size: 1.25rem;
    }

    .hero-subtitle .nepali-text {
        font-size: 1.125rem;
    }

    .hero-subtitle .english-text {
        font-size: 1rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        line-height: 1.5;
    }

    .hero-icon {
        font-size: 1.75rem;
    }

    .hero-stats {
        flex-direction: row;
        gap: 0.75rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-stats .stat-item {
        padding: 0.625rem 0.875rem;
        min-width: 120px;
        flex: 0 0 auto;
    }

    .hero-stats .stat-item strong {
        font-size: 1.125rem;
    }

    .hero-stats .stat-item .stat-label {
        font-size: 0.8rem;
    }

    /* Quick Search Small Mobile */
    .quick-search-section {
        padding: 1.5rem 0;
    }

    .quick-search-input-group {
        padding: 0.75rem;
    }

    .quick-search-input {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }

    .quick-search-btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }

    /* Stats Small Mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1.25rem 0.75rem;
    }

    .stat-icon {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .container {
        padding: 0 0.75rem;
    }

    /* Gallery Mobile Styles for Small Screens */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .gallery-image-container {
        height: 300px;
    }

    .gallery-overlay {
        transform: translateY(0);
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
        padding: 1.5rem 1rem 1rem;
    }

    .gallery-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .gallery-view-btn,
    .gallery-details-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Mobile Menu Styles */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: background var(--transition-fast);
}

.mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.hamburger-line {
    width: 22px;
    height: 2px;
    background: var(--white);
    margin: 2px 0;
    transition: all var(--transition-fast);
    border-radius: 1px;
}

.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--primary-color);
    box-shadow: var(--shadow-lg);
    z-index: 999;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu.active {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-nav-menu {
    list-style: none;
    padding: 0.5rem 0;
    margin: 0;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--white);
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-fast);
    font-size: 0.9rem;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    padding-left: 2rem;
}

.mobile-nav-link:last-child {
    border-bottom: none;
}

.mobile-nav-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

/* Flash Messages */
.flash-message {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem 0;
    transition: all var(--transition-normal);
}

.flash-success {
    background: var(--success-color);
    color: var(--white);
}

.flash-error {
    background: var(--error-color);
    color: var(--white);
}

.flash-warning {
    background: var(--warning-color);
    color: var(--white);
}

.flash-info {
    background: var(--accent-color);
    color: var(--white);
}

.flash-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.flash-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: background var(--transition-fast);
}

.flash-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-indicator.visible {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--medium-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

/* Skip to Content Link */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: 1001;
    transition: top var(--transition-fast);
}

.skip-to-content:focus {
    top: 6px;
}

/* Image Modal */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    padding: 2rem;
}

.image-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: pointer;
}

.image-modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.image-modal-content img {
    width: 100%;
    height: auto;
    display: block;
}

.image-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background var(--transition-fast);
}

.image-modal-close:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Enhanced Gallery Modal */
.gallery-modal .image-modal-content {
    max-width: 95vw;
    max-height: 95vh;
    display: flex;
    flex-direction: row;
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.gallery-modal-image {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    min-height: 500px;
    position: relative;
}

.gallery-modal-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--radius-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.gallery-modal-details {
    flex: 0 0 380px;
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    background: var(--white);
    border-left: 1px solid var(--light-gray);
    overflow-y: auto;
}

.gallery-modal-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    line-height: 1.3;
    font-family: var(--devanagari-font);
}

.gallery-modal-author {
    font-size: 1.25rem;
    color: var(--text-dark);
    margin: 0;
    font-weight: 500;
}

.gallery-modal-meta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background: var(--secondary-color);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
}

.meta-item {
    font-size: 0.95rem;
    color: var(--text-dark);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(85, 8, 12, 0.1);
}

.meta-item:last-child {
    border-bottom: none;
}

.meta-item strong {
    color: var(--primary-color);
    font-weight: 600;
    min-width: 80px;
}

.gallery-modal-actions {
    margin-top: auto;
    padding-top: 1.5rem;
    display: flex;
    gap: 1rem;
}

.gallery-modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.gallery-modal-nav:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
}

.gallery-modal-nav.prev {
    left: 20px;
}

.gallery-modal-nav.next {
    right: 20px;
}

/* Mobile Gallery Modal */
@media (max-width: 768px) {
    .gallery-modal .image-modal-content {
        flex-direction: column;
        max-width: 95vw;
        max-height: 95vh;
        border-radius: var(--radius-lg);
    }

    .gallery-modal-image {
        flex: 1;
        min-height: 300px;
    }

    .gallery-modal-details {
        flex: 0 0 auto;
        border-left: none;
        border-top: 1px solid var(--light-gray);
        padding: 1.5rem;
        max-height: 40vh;
        overflow-y: auto;
    }

    .gallery-modal-title {
        font-size: 1.5rem;
    }

    .gallery-modal-author {
        font-size: 1.125rem;
    }

    .gallery-modal-meta {
        padding: 1rem;
    }

    .gallery-modal-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .gallery-modal-nav {
        width: 40px;
        height: 40px;
    }

    .gallery-modal-nav.prev {
        left: 10px;
    }

    .gallery-modal-nav.next {
        right: 10px;
    }
}

/* Notifications */
.notification {
    position: fixed;
    top: 100px;
    right: 2rem;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 1rem;
    max-width: 400px;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
}

.notification-info {
    border-left: 4px solid var(--accent-color);
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.notification-close {
    background: none;
    border: none;
    color: var(--dark-gray);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: background var(--transition-fast);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--light-gray);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Footer Links */
.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--light-accent);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-stats {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    font-size: 0.875rem;
}

/* Gallery Styles */
.gallery-hero-section {
    background: linear-gradient(135deg, #2c0a0e 0%, var(--primary-color) 30%, #8B1538 70%, #a91d42 100%);
    color: var(--white);
    padding: 4rem 0 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 400px;
    display: flex;
    align-items: center;
}

.gallery-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.2) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="books" width="40" height="40" patternUnits="userSpaceOnUse"><rect width="8" height="12" x="5" y="14" fill="rgba(0,0,0,0.1)" rx="1"/><rect width="8" height="12" x="15" y="12" fill="rgba(0,0,0,0.08)" rx="1"/><rect width="8" height="12" x="25" y="16" fill="rgba(0,0,0,0.09)" rx="1"/></pattern></defs><rect width="100" height="100" fill="url(%23books)"/></svg>');
    opacity: 0.4;
}

.gallery-hero-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
}

.gallery-hero-content {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 2rem;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.gallery-title {
    margin-bottom: 1.5rem;
    line-height: 1.2;
    position: relative;
}

.gallery-title .nepali-text {
    display: block;
    font-family: var(--devanagari-font);
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #ffffff;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 0 20px rgba(255, 255, 255, 0.3);
    position: relative;
}

.gallery-title .nepali-text::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ffffff, transparent);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.gallery-title .english-text {
    font-size: 1.75rem;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
}

.gallery-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: #ffffff;
    line-height: 1.7;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8);
    font-weight: 300;
}

.gallery-stats,
.page-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 1s ease-out;
    color: #ffffff;
}

.stat-item:nth-child(1) {
    animation-delay: 0.2s;
}

.stat-item:nth-child(2) {
    animation-delay: 0.4s;
}

.stat-item:nth-child(3) {
    animation-delay: 0.6s;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.stat-item:hover::before {
    left: 100%;
}

.stat-item:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.5);
}

.stat-item strong {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.stat-item .stat-label {
    font-size: 0.9rem;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Gallery Hero Icons */
.gallery-hero-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.gallery-hero-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.15);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
}

.gallery-hero-icon:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.gallery-hero-icon:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.gallery-hero-icon:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.15;
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
        opacity: 0.25;
    }
}

/* Gallery Filters */
.gallery-filters-section {
    background: var(--white);
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--light-gray);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.gallery-filters-form {
    max-width: 1200px;
    margin: 0 auto;
}

.filters-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto auto;
    gap: 1rem;
    align-items: center;
}

.filter-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
}

.filter-input:focus {
    outline: none;
    border-color: var(--accent-color);
}

.filter-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--white);
    cursor: pointer;
    transition: border-color var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--accent-color);
}

.filter-btn {
    background: var(--accent-color);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background var(--transition-fast);
}

.filter-btn:hover {
    background: var(--primary-color);
}

.filter-icon {
    width: 20px;
    height: 20px;
}

.clear-filters-btn {
    color: var(--text-light);
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    transition: background var(--transition-fast);
}

.clear-filters-btn:hover {
    background: var(--light-gray);
}

/* Gallery Grid */
.gallery-section {
    padding: 2.5rem 0;
    background: var(--secondary-color);
    min-height: 60vh;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--white);
    cursor: pointer;
    aspect-ratio: 3/4;
}

.gallery-item:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.gallery-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: var(--light-gray);
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.08);
    filter: brightness(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.1) 40%,
        rgba(0, 0, 0, 0.8) 100%
    );
    color: var(--white);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-info {
    text-align: center;
    transform: translateY(20px);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-item:hover .gallery-info {
    transform: translateY(0);
}

.gallery-book-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.gallery-book-author {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.gallery-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 0.5rem;
}

.gallery-view-btn,
.gallery-details-btn {
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
    border: 1px solid rgba(255, 255, 255, 0.25);
    padding: 0.5rem 0.875rem;
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.gallery-view-btn:hover,
.gallery-details-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    color: var(--white);
    transform: translateY(-2px);
}

/* Gallery Loading State */
.gallery-image.loading {
    opacity: 0.7;
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    animation: loading-shimmer 1.5s infinite linear;
}

.gallery-image.loaded {
    opacity: 1;
    transition: opacity 0.3s ease-out;
}

@keyframes loading-shimmer {
    0% {
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    }
    100% {
        background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px;
    }
}

/* Gallery No Results */
.gallery-no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-light);
}

.gallery-no-results .no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.gallery-no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.gallery-no-results p {
    font-size: 1rem;
    margin-bottom: 2rem;
}

.gallery-no-results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Gallery Pagination */
.gallery-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2.5rem;
    flex-wrap: wrap;
}

.pagination-btn {
    padding: 0.75rem 1rem;
    background: var(--white);
    color: var(--text-dark);
    text-decoration: none;
    border-radius: var(--radius-md);
    border: 1px solid var(--light-gray);
    transition: all 0.3s ease;
    font-weight: 500;
    min-width: 44px;
    text-align: center;
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* Mobile Responsive Updates */
@media (max-width: 768px) {
    body {
        padding-top: 60px;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .header {
        height: 60px;
    }

    .header-content {
        padding: 0 0.75rem;
    }

    .logo {
        gap: 0.5rem;
    }

    .logo-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .logo-text h1 {
        font-size: 1.1rem;
    }

    .logo-text p {
        font-size: 0.7rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-stats {
        justify-content: center;
    }

    .notification {
        right: 1rem;
        left: 1rem;
        max-width: none;
        top: 80px;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
    }

    .flash-message {
        top: 70px;
    }

    /* Catalog Mobile Styles */
    .catalog-header {
        padding: 3.5rem 0 2.5rem;
        min-height: 350px;
    }

    .catalog-header-content {
        padding: 0 1rem;
    }

    .catalog-title .nepali-text {
        font-size: 3rem;
        margin-bottom: 0.5rem;
    }

    .catalog-title .english-text {
        font-size: 1.5rem;
    }

    .catalog-description {
        font-size: 1.125rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .catalog-stats {
        display: flex;
        flex-direction: row;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
    }

    .catalog-stats .stat-item {
        padding: 0.75rem 1rem;
        min-width: 120px;
        flex: 0 0 auto;
    }

    .catalog-stats .stat-item strong {
        font-size: 1.5rem;
    }

    .catalog-stats .stat-item span {
        font-size: 0.85rem;
    }

    .catalog-icon {
        font-size: 2rem;
    }

    /* Quick Search Mobile */
    .quick-search-input-group {
        flex-direction: column;
        gap: 1rem;
    }

    .quick-search-btn {
        width: 100%;
        justify-content: center;
    }

    /* Stats Mobile */
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-icon {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    /* Gallery Mobile Styles */
    .gallery-hero-section {
        padding: 3rem 0 2rem;
        min-height: 300px;
    }

    .gallery-hero-content {
        padding: 0 1rem;
    }

    .gallery-title .nepali-text {
        font-size: 2.75rem;
        margin-bottom: 0.5rem;
    }

    .gallery-title .english-text {
        font-size: 1.25rem;
    }

    .gallery-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .gallery-stats {
        flex-direction: row;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .stat-item {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
        min-width: 200px;
        text-align: center;
    }

    .stat-item strong {
        font-size: 1.25rem;
    }

    .stat-item .stat-label {
        font-size: 0.85rem;
    }

    .gallery-hero-icon {
        font-size: 1.5rem;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
    }

    .gallery-item {
        aspect-ratio: 3/4.5;
    }
}

/* Tablet Responsive Updates */
@media (max-width: 1024px) and (min-width: 769px) {
    .header-content {
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 0.75rem;
    }

    .nav-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }

    .logo-text h1 {
        font-size: 1.15rem;
    }

    .logo-text p {
        font-size: 0.7rem;
    }

    /* Gallery Tablet Styles */
    .gallery-title .nepali-text {
        font-size: 3.5rem;
    }

    .gallery-title .english-text {
        font-size: 1.5rem;
    }

    .gallery-description {
        font-size: 1.125rem;
    }

    .gallery-stats {
        gap: 1.5rem;
    }

    .stat-item {
        padding: 0.875rem 1.25rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 1.25rem;
    }

    /* Catalog Tablet Styles */
    .catalog-title .nepali-text {
        font-size: 3.5rem;
    }

    .catalog-title .english-text {
        font-size: 1.5rem;
    }

    .catalog-description {
        font-size: 1.125rem;
    }

    .catalog-stats {
        gap: 1rem;
        flex-wrap: wrap;
    }

    .catalog-stats .stat-item {
        padding: 0.875rem 1rem;
        min-width: 100px;
        flex: 0 0 auto;
    }
}
