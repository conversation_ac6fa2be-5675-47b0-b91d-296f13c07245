<?php
/**
 * मैथिली विकास कोष - <PERSON>thi<PERSON>
 * About Page
 */

$page_title = 'About Us - ' . APP_NAME;
$page_description = 'Learn about मैथिली विकास कोष and our mission to preserve and promote Maithili literature and cultural heritage.';

include 'templates/header.php';
?>

<main class="main-content">
    <!-- Hero Section -->
    <section class="hero-section">
        <!-- Floating Icons -->
        <div class="hero-icons">
            <div class="hero-icon">🏛️</div>
            <div class="hero-icon">📚</div>
            <div class="hero-icon">🎓</div>
            <div class="hero-icon">🌟</div>
        </div>

        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="nepali-text">हाम्रो बारेमा</span>
                    <span class="english-text">About Us</span>
                </h1>
                <p class="hero-description">
                    Dedicated to preserving and promoting the rich heritage of Maithili literature and culture. Discover our mission, vision, and commitment to cultural preservation through digital innovation.
                </p>
                <div class="catalog-stats">
                    <?php
                    try {
                        $db = new Database();
                        $about_books = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE isDeleted = 0") ?: 0;
                        $about_authors = $db->fetchColumn("SELECT COUNT(*) FROM authors WHERE isDeleted = 0") ?: 0;
                        $about_publishers = $db->fetchColumn("SELECT COUNT(*) FROM publishers WHERE isDeleted = 0") ?: 0;
                        $about_categories = $db->fetchColumn("SELECT COUNT(*) FROM categories WHERE isDeleted = 0") ?: 0;
                        $about_languages = $db->fetchColumn("SELECT COUNT(*) FROM languages WHERE isDeleted = 0") ?: 0;
                    ?>
                        <div class="stat-item">
                            <strong><?= number_format($about_books) ?></strong>
                            <span>Books Preserved</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($about_categories) ?></strong>
                            <span>Categories</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($about_authors) ?></strong>
                            <span>Authors Featured</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($about_publishers) ?></strong>
                            <span>Publishers</span>
                        </div>
                        <div class="stat-item">
                            <strong><?= number_format($about_languages) ?></strong>
                            <span>Languages</span>
                        </div>
                    <?php
                    } catch (Exception $e) {
                        // Fallback stats if database is unavailable
                    ?>
                        <div class="stat-item">
                            <strong>1000+</strong>
                            <span>Books Preserved</span>
                        </div>
                        <div class="stat-item">
                            <strong>50+</strong>
                            <span>Categories</span>
                        </div>
                        <div class="stat-item">
                            <strong>100+</strong>
                            <span>Authors Featured</span>
                        </div>
                        <div class="stat-item">
                            <strong>25+</strong>
                            <span>Publishers</span>
                        </div>
                        <div class="stat-item">
                            <strong>5+</strong>
                            <span>Languages</span>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </section>

    <!-- About Content -->
    <section class="about-section">
        <div class="container">
            <div class="about-grid">
                <!-- Mission Section -->
                <div class="about-card">
                    <div class="about-icon">
                        <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3>
                        <span class="nepali-text">हाम्रो लक्ष्य</span>
                        <span class="english-text">Our Mission</span>
                    </h3>
                    <p>
                        To preserve, digitize, and make accessible the vast treasure of Maithili literature and cultural heritage. 
                        We aim to bridge the gap between traditional knowledge and modern accessibility, ensuring that future 
                        generations can connect with their cultural roots.
                    </p>
                    <p class="nepali-text">
                        मैथिली साहित्य र सांस्कृतिक सम्पदाको संरक्षण, डिजिटलीकरण र पहुँचयोग्य बनाउनु हाम्रो मुख्य उद्देश्य हो।
                    </p>
                </div>

                <!-- Vision Section -->
                <div class="about-card">
                    <div class="about-icon">
                        <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                    </div>
                    <h3>
                        <span class="nepali-text">हाम्रो दृष्टिकोण</span>
                        <span class="english-text">Our Vision</span>
                    </h3>
                    <p>
                        To become the premier digital repository for Maithili literature, serving as a comprehensive 
                        research center that supports scholars, students, and enthusiasts worldwide in their pursuit 
                        of knowledge about Maithili culture and literature.
                    </p>
                    <p class="nepali-text">
                        मैथिली साहित्यको प्रमुख डिजिटल भण्डार बनी विश्वभरका अनुसन्धानकर्ता र विद्यार्थीहरूलाई सेवा प्रदान गर्नु।
                    </p>
                </div>

                <!-- History Section -->
                <div class="about-card">
                    <div class="about-icon">
                        <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3>
                        <span class="nepali-text">हाम्रो इतिहास</span>
                        <span class="english-text">Our History</span>
                    </h3>
                    <p>
                        Established with the noble goal of preserving the literary heritage of Mithila, our library 
                        has grown from a small collection to a comprehensive digital archive. We honor the legacy 
                        of great poets like Vidyapati while embracing modern technology.
                    </p>
                    <p class="nepali-text">
                        मिथिलाको साहित्यिक सम्पदाको संरक्षणको उद्देश्यले स्थापना भएको यो पुस्तकालय आज एक व्यापक डिजिटल संग्रहालय बनेको छ।
                    </p>
                </div>

                <!-- Services Section -->
                <div class="about-card">
                    <div class="about-icon">
                        <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                    </div>
                    <h3>
                        <span class="nepali-text">हाम्रा सेवाहरू</span>
                        <span class="english-text">Our Services</span>
                    </h3>
                    <ul class="services-list">
                        <li>Digital book catalog and search</li>
                        <li>Research assistance and guidance</li>
                        <li>Cultural heritage documentation</li>
                        <li>Academic collaboration programs</li>
                        <li>Educational workshops and seminars</li>
                        <li>Manuscript digitization services</li>
                    </ul>
                </div>

                <!-- Team Section -->
                <div class="about-card">
                    <div class="about-icon">
                        <svg width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H17c-.8 0-1.54.37-2.01.99L14 10l-1.99-1.01A2.5 2.5 0 0 0 10 8H8.46c-.8 0-1.54.37-2.01.99L3.5 16H6v6h4v-6h4v6h4z"/>
                        </svg>
                    </div>
                    <h3>
                        <span class="nepali-text">हाम्रो टोली</span>
                        <span class="english-text">Our Team</span>
                    </h3>
                    <p>
                        Our dedicated team consists of librarians, scholars, technologists, and cultural enthusiasts 
                        who work tirelessly to maintain and expand our collection. We are committed to excellence 
                        in preserving and sharing Maithili heritage.
                    </p>
                    <div class="team-member">
                        <h4 class="nepali-text">श्रद्धेय वात्स्यायन</h4>
                        <p>Lead Developer & Cultural Preservationist</p>
                        <p>📞 9844361480</p>
                    </div>
                </div>

                <!-- Collection Stats -->
                <div class="about-card stats-card">
                    <h3>
                        <span class="nepali-text">हाम्रो संग्रह</span>
                        <span class="english-text">Our Collection</span>
                    </h3>
                    <div class="stats-grid">
                        <?php
                        try {
                            $db = new Database();
                            $collection_books = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE isDeleted = 0") ?: 0;
                            $collection_authors = $db->fetchColumn("SELECT COUNT(*) FROM authors WHERE isDeleted = 0") ?: 0;
                            $collection_publishers = $db->fetchColumn("SELECT COUNT(*) FROM publishers WHERE isDeleted = 0") ?: 0;
                            $collection_categories = $db->fetchColumn("SELECT COUNT(*) FROM categories WHERE isDeleted = 0") ?: 0;
                            $collection_languages = $db->fetchColumn("SELECT COUNT(*) FROM languages WHERE isDeleted = 0") ?: 0;

                            $display_stats = [
                                ['label' => 'Total Books', 'nepali' => 'कुल पुस्तकहरू', 'count' => $collection_books],
                                ['label' => 'Authors', 'nepali' => 'लेखकहरू', 'count' => $collection_authors],
                                ['label' => 'Publishers', 'nepali' => 'प्रकाशकहरू', 'count' => $collection_publishers],
                                ['label' => 'Categories', 'nepali' => 'श्रेणीहरू', 'count' => $collection_categories],
                                ['label' => 'Languages', 'nepali' => 'भाषाहरू', 'count' => $collection_languages]
                            ];

                            foreach ($display_stats as $stat):
                        ?>
                            <div class="stat-item">
                                <div class="stat-number"><?= number_format($stat['count']) ?></div>
                                <div class="stat-label">
                                    <span class="nepali-text"><?= $stat['nepali'] ?></span>
                                    <span class="english-text"><?= $stat['label'] ?></span>
                                </div>
                            </div>
                        <?php
                            endforeach;
                        } catch (Exception $e) {
                            echo '<p>Statistics temporarily unavailable.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>
                    <span class="nepali-text">हाम्रो संग्रह अन्वेषण गर्नुहोस्</span>
                    <span class="english-text">Explore Our Collection</span>
                </h2>
                <p>
                    Discover thousands of books, manuscripts, and cultural documents that tell the story of Maithili heritage.
                </p>
                <div class="cta-buttons">
                    <a href="?page=catalog" class="btn btn-primary">Browse Books</a>
                    <a href="?page=contact" class="btn btn-secondary">Contact Us</a>
                </div>
            </div>
        </div>
    </section>
</main>

<style>
.about-section {
    padding: 4rem 0;
    background: var(--white);
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.about-card {
    background: var(--secondary-color);
    border-radius: var(--radius-xl);
    padding: 2rem;
    border: 1px solid var(--medium-gray);
    transition: all var(--transition-normal);
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.about-icon {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.about-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.about-card h3 .nepali-text {
    display: block;
    margin-bottom: 0.25rem;
}

.services-list {
    list-style: none;
    padding-left: 0;
}

.services-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--medium-gray);
    position: relative;
    padding-left: 1.5rem;
}

.services-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.services-list li:last-child {
    border-bottom: none;
}

.team-member {
    background: var(--white);
    padding: 1rem;
    border-radius: var(--radius-md);
    margin-top: 1rem;
    border: 1px solid var(--medium-gray);
}

.team-member h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white);
}

.stats-card h3 {
    color: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: var(--radius-md);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--light-accent);
    margin-bottom: 0.5rem;
}

.stat-label .nepali-text {
    display: block;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.stat-label .english-text {
    font-size: 0.75rem;
    opacity: 0.8;
}

.cta-section {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--white) 100%);
    padding: 4rem 0;
    text-align: center;
}

.cta-content h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.cta-content h2 .nepali-text {
    display: block;
    margin-bottom: 0.5rem;
}

.cta-content p {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .about-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-buttons .btn {
        width: 200px;
    }
}
</style>

<?php include 'templates/footer.php'; ?>
