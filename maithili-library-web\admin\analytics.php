<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Analytics Dashboard
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

$message = '';
$error = '';

// Initialize default values
$monthly_additions = [];
$category_distribution = [];
$language_distribution = [];
$condition_analysis = [];
$source_analysis = [];
$location_utilization = [];
$recent_activity = [
    'books_this_month' => 0,
    'books_last_month' => 0,
    'authors_this_month' => 0,
    'categories_this_month' => 0
];

// Get analytics data
try {
    // Growth analytics - books added over time
    $monthly_additions = $db->fetchAll("
        SELECT
            DATE_FORMAT(createdAt, '%Y-%m') as month,
            COUNT(*) as bookCount
        FROM books
        WHERE isDeleted = 0
        AND createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
        ORDER BY month ASC
    ");

    // Collection distribution
    $category_distribution = $db->fetchAll("
        SELECT c.name, COUNT(b.id) as bookCount,
               ROUND((COUNT(b.id) * 100.0 / (SELECT COUNT(*) FROM books WHERE isDeleted = 0)), 2) as percentage
        FROM categories c
        LEFT JOIN books b ON c.id = b.categoryId AND b.isDeleted = 0
        WHERE c.isDeleted = 0
        GROUP BY c.id
        HAVING bookCount > 0
        ORDER BY bookCount DESC
        LIMIT 8
    ");

    // Language distribution
    $language_distribution = $db->fetchAll("
        SELECT l.name, l.nameNepali, COUNT(b.id) as bookCount,
               ROUND((COUNT(b.id) * 100.0 / (SELECT COUNT(*) FROM books WHERE isDeleted = 0)), 2) as percentage
        FROM languages l
        LEFT JOIN books b ON l.id = b.languageId AND b.isDeleted = 0
        WHERE l.isDeleted = 0
        GROUP BY l.id
        HAVING bookCount > 0
        ORDER BY bookCount DESC
        LIMIT 6
    ");

    // Condition analysis
    $condition_analysis = $db->fetchAll("
        SELECT c.name, COUNT(b.id) as bookCount,
               ROUND((COUNT(b.id) * 100.0 / (SELECT COUNT(*) FROM books WHERE isDeleted = 0)), 2) as percentage
        FROM conditions c
        LEFT JOIN books b ON c.id = b.conditionId AND b.isDeleted = 0
        GROUP BY c.id
        ORDER BY c.sortOrder ASC, c.name ASC
    ");

    // Source analysis
    $source_analysis = $db->fetchAll("
        SELECT s.name, s.type, COUNT(b.id) as bookCount
        FROM sources s
        LEFT JOIN books b ON s.id = b.sourceId AND b.isDeleted = 0
        WHERE s.isDeleted = 0
        GROUP BY s.id
        HAVING bookCount > 0
        ORDER BY bookCount DESC
        LIMIT 10
    ");

    // Location utilization - Fixed SQL query
    $location_utilization = $db->fetchAll("
        SELECT l.shelf, l.row, l.capacity, COUNT(b.id) as currentBooks,
               CASE
                   WHEN l.capacity > 0 THEN ROUND((COUNT(b.id) * 100.0 / l.capacity), 2)
                   ELSE NULL
               END as utilizationPercentage
        FROM locations l
        LEFT JOIN books b ON l.id = b.locationId AND b.isDeleted = 0
        WHERE l.isDeleted = 0
        GROUP BY l.id, l.shelf, l.row, l.capacity
        ORDER BY
            CASE WHEN l.capacity > 0 THEN 0 ELSE 1 END,
            CASE WHEN l.capacity > 0 THEN ROUND((COUNT(b.id) * 100.0 / l.capacity), 2) ELSE 0 END DESC,
            COUNT(b.id) DESC
        LIMIT 10
    ");

    // Recent activity summary
    $recent_activity = [
        'books_this_month' => (int)$db->fetchColumn("
            SELECT COUNT(*) FROM books
            WHERE isDeleted = 0 AND createdAt >= DATE_FORMAT(NOW(), '%Y-%m-01')
        ") ?: 0,
        'books_last_month' => (int)$db->fetchColumn("
            SELECT COUNT(*) FROM books
            WHERE isDeleted = 0
            AND createdAt >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01')
            AND createdAt < DATE_FORMAT(NOW(), '%Y-%m-01')
        ") ?: 0,
        'authors_this_month' => (int)$db->fetchColumn("
            SELECT COUNT(*) FROM authors
            WHERE isDeleted = 0 AND createdAt >= DATE_FORMAT(NOW(), '%Y-%m-01')
        ") ?: 0,
        'categories_this_month' => (int)$db->fetchColumn("
            SELECT COUNT(*) FROM categories
            WHERE isDeleted = 0 AND createdAt >= DATE_FORMAT(NOW(), '%Y-%m-01')
        ") ?: 0
    ];

} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    // log_activity("Analytics error: " . $e->getMessage(), 'ERROR');
}

$page_title = 'Analytics - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link active">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="contact_inquiries.php" class="nav-link">
                            <span class="nav-icon">💬</span>
                            Contact Inquiries
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>

            <!-- Header -->
            <header class="admin-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">📊</span>
                            Analytics
                        </h1>
                        <p class="header-subtitle">Collection insights and trends</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Analytics</span>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <!-- Activity Summary -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">📚</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($recent_activity['books_this_month'] ?? 0) ?></div>
                            <div class="stat-label">Books Added This Month</div>
                            <?php if (isset($recent_activity['books_last_month']) && $recent_activity['books_last_month'] > 0): ?>
                                <?php
                                $growth = $recent_activity['books_this_month'] - $recent_activity['books_last_month'];
                                $growth_percent = round(($growth / $recent_activity['books_last_month']) * 100, 1);
                                ?>
                                <div class="stat-change <?= $growth >= 0 ? 'positive' : 'negative' ?>">
                                    <?= $growth >= 0 ? '↗' : '↘' ?> <?= abs($growth_percent) ?>% from last month
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="stat-card accent">
                        <div class="stat-icon">✍️</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($recent_activity['authors_this_month'] ?? 0) ?></div>
                            <div class="stat-label">Authors Added This Month</div>
                        </div>
                    </div>
                    <div class="stat-card info">
                        <div class="stat-icon">🏷️</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($recent_activity['categories_this_month'] ?? 0) ?></div>
                            <div class="stat-label">Categories Added This Month</div>
                        </div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= count($monthly_additions) ?></div>
                            <div class="stat-label">Active Months (Last 12)</div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Grid -->
                <div class="analytics-grid">
                    <!-- Monthly Growth -->
                    <div class="card analytics-card">
                        <div class="card-header">
                            <h3 class="card-title">Monthly Book Additions</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Last 12 months</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($monthly_additions)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">📈</div>
                                    <p>No data available</p>
                                </div>
                            <?php else: ?>
                                <div class="chart-container">
                                    <div class="chart-bars">
                                        <?php
                                        $max_books = max(array_column($monthly_additions, 'bookCount'));
                                        foreach ($monthly_additions as $month_data):
                                            $height = $max_books > 0 ? ($month_data['bookCount'] / $max_books) * 100 : 0;
                                        ?>
                                            <div class="chart-bar">
                                                <div class="bar-fill" style="height: <?= $height ?>%"></div>
                                                <div class="bar-value"><?= $month_data['bookCount'] ?></div>
                                                <div class="bar-label"><?= date('M Y', strtotime($month_data['month'] . '-01')) ?></div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Category Distribution -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Category Distribution</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Top categories by percentage</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($category_distribution)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">🏷️</div>
                                    <p>No categories found</p>
                                </div>
                            <?php else: ?>
                                <div class="distribution-list">
                                    <?php foreach ($category_distribution as $category): ?>
                                        <div class="distribution-item">
                                            <div class="distribution-info">
                                                <div class="distribution-name"><?= htmlspecialchars($category['name']) ?></div>
                                                <div class="distribution-stats">
                                                    <span class="book-count"><?= number_format($category['bookCount']) ?> books</span>
                                                    <span class="percentage"><?= $category['percentage'] ?>%</span>
                                                </div>
                                            </div>
                                            <div class="distribution-bar">
                                                <div class="bar-fill" style="width: <?= $category['percentage'] ?>%"></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Language Distribution -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Language Distribution</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Books by language</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($language_distribution)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">🌐</div>
                                    <p>No languages found</p>
                                </div>
                            <?php else: ?>
                                <div class="distribution-list">
                                    <?php foreach ($language_distribution as $language): ?>
                                        <div class="distribution-item">
                                            <div class="distribution-info">
                                                <div class="distribution-name">
                                                    <?= htmlspecialchars($language['name']) ?>
                                                    <?php if (!empty($language['nameNepali'])): ?>
                                                        <span class="nepali-text">(<?= htmlspecialchars($language['nameNepali']) ?>)</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="distribution-stats">
                                                    <span class="book-count"><?= number_format($language['bookCount']) ?> books</span>
                                                    <span class="percentage"><?= $language['percentage'] ?>%</span>
                                                </div>
                                            </div>
                                            <div class="distribution-bar">
                                                <div class="bar-fill" style="width: <?= $language['percentage'] ?>%"></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Condition Analysis -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Collection Condition</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Books by condition</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($condition_analysis)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">🔧</div>
                                    <p>No condition data</p>
                                </div>
                            <?php else: ?>
                                <div class="condition-grid">
                                    <?php foreach ($condition_analysis as $condition): ?>
                                        <div class="condition-item">
                                            <div class="condition-name"><?= htmlspecialchars($condition['name']) ?></div>
                                            <div class="condition-count"><?= number_format($condition['bookCount']) ?></div>
                                            <div class="condition-percentage"><?= $condition['percentage'] ?>%</div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Additional Analytics -->
                <div class="analytics-grid-2">
                    <!-- Source Analysis -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Top Sources</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">By book count</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($source_analysis)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">🎁</div>
                                    <p>No source data</p>
                                </div>
                            <?php else: ?>
                                <div class="source-list">
                                    <?php foreach ($source_analysis as $source): ?>
                                        <div class="source-item">
                                            <div class="source-info">
                                                <div class="source-name"><?= htmlspecialchars($source['name']) ?></div>
                                                <?php if (!empty($source['type'])): ?>
                                                    <div class="source-type"><?= htmlspecialchars(ucfirst($source['type'])) ?></div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="source-count">
                                                <span class="badge badge-primary"><?= number_format($source['bookCount']) ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Location Utilization -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Location Utilization</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Capacity usage</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($location_utilization)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">📍</div>
                                    <p>No location data</p>
                                </div>
                            <?php else: ?>
                                <div class="location-list">
                                    <?php foreach ($location_utilization as $location): ?>
                                        <div class="location-item">
                                            <div class="location-info">
                                                <div class="location-name">
                                                    <?= htmlspecialchars($location['shelf']) ?>
                                                    <?php if (!empty($location['row'])): ?>
                                                        - <?= htmlspecialchars($location['row']) ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="location-stats">
                                                    <?= number_format($location['currentBooks']) ?> books
                                                    <?php if ($location['capacity']): ?>
                                                        / <?= number_format($location['capacity']) ?> capacity
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="location-utilization">
                                                <?php if ($location['utilizationPercentage'] !== null): ?>
                                                    <div class="utilization-bar">
                                                        <div class="bar-fill" style="width: <?= min(100, $location['utilizationPercentage']) ?>%"></div>
                                                    </div>
                                                    <span class="utilization-text"><?= $location['utilizationPercentage'] ?>%</span>
                                                <?php else: ?>
                                                    <span class="text-muted">No capacity set</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <script>
        // Mobile Menu Functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile menu toggle button event listener
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            // Mobile overlay click event listener
            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile close button event listener
            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });

            // Handle escape key to close modal
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeMobileMenu();
                }
            });

            console.log('Analytics page loaded successfully');
        });

        // Close mobile menu on window resize if screen becomes large
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });
    </script>
</body>
</html>
