<?php
/**
 * मैथिली विकास कोष - Admin Navigation Test
 * Test script to verify admin navigation and contact inquiries access
 */

require_once '../config/config.php';
require_once '../includes/Database.php';

echo "<h1>Admin Navigation Test</h1>\n";

// Test admin pages that should have contact inquiries link
$admin_pages = [
    'dashboard.php' => 'Admin Dashboard',
    'books.php' => 'Books Management',
    'authors.php' => 'Authors Management',
    'reports.php' => 'Reports & Analytics',
    'analytics.php' => 'Analytics',
    'contact_inquiries.php' => 'Contact Inquiries'
];

echo "<h2>✅ Admin Pages Navigation Test</h2>\n";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
echo "<tr><th>Page</th><th>Description</th><th>File Exists</th><th>Access URL</th></tr>\n";

foreach ($admin_pages as $page => $description) {
    $file_exists = file_exists($page);
    $status_color = $file_exists ? 'green' : 'red';
    $status_icon = $file_exists ? '✓' : '❌';
    
    echo "<tr>";
    echo "<td><strong>$page</strong></td>";
    echo "<td>$description</td>";
    echo "<td style='color: $status_color;'>$status_icon " . ($file_exists ? 'Exists' : 'Missing') . "</td>";
    echo "<td><a href='$page' target='_blank'>Open Page</a></td>";
    echo "</tr>\n";
}
echo "</table>\n";

// Test contact inquiries functionality
echo "<h2>✅ Contact Inquiries Functionality Test</h2>\n";

try {
    $db = new Database();
    
    // Check if contact_inquiries table exists
    $table_exists = $db->tableExists('contact_inquiries');
    echo "<p style='color: " . ($table_exists ? 'green' : 'red') . ";'>";
    echo ($table_exists ? '✓' : '❌') . " Contact inquiries table: " . ($table_exists ? 'Exists' : 'Missing');
    echo "</p>\n";
    
    if ($table_exists) {
        // Get inquiry count
        $total_inquiries = $db->fetchColumn("SELECT COUNT(*) FROM contact_inquiries");
        echo "<p><strong>Total contact inquiries:</strong> $total_inquiries</p>\n";
        
        // Get recent inquiries
        if ($total_inquiries > 0) {
            $recent_inquiries = $db->fetchAll("
                SELECT name, email, subject, status, createdAt 
                FROM contact_inquiries 
                ORDER BY createdAt DESC 
                LIMIT 5
            ");
            
            echo "<h3>Recent Contact Inquiries:</h3>\n";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
            echo "<tr><th>Name</th><th>Email</th><th>Subject</th><th>Status</th><th>Created</th></tr>\n";
            
            foreach ($recent_inquiries as $inquiry) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($inquiry['name']) . "</td>";
                echo "<td>" . htmlspecialchars($inquiry['email']) . "</td>";
                echo "<td>" . htmlspecialchars(substr($inquiry['subject'], 0, 40)) . "...</td>";
                echo "<td><span style='padding: 2px 6px; background: " . 
                     ($inquiry['status'] === 'NEW' ? '#fef3c7' : '#dcfce7') . 
                     "; border-radius: 4px; font-size: 12px;'>" . 
                     htmlspecialchars($inquiry['status']) . "</span></td>";
                echo "<td>" . htmlspecialchars($inquiry['createdAt']) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p><em>No contact inquiries found. Users can submit inquiries through the contact form.</em></p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test navigation links in admin pages
echo "<h2>✅ Navigation Links Test</h2>\n";

$pages_with_nav = ['dashboard.php', 'books.php', 'authors.php', 'reports.php', 'analytics.php'];
$contact_link_found = 0;
$total_pages_checked = 0;

foreach ($pages_with_nav as $page) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        $has_contact_link = strpos($content, 'contact_inquiries.php') !== false;
        
        echo "<p>";
        echo ($has_contact_link ? '✓' : '❌') . " ";
        echo "<strong>$page:</strong> ";
        echo ($has_contact_link ? 'Has contact inquiries link' : 'Missing contact inquiries link');
        echo "</p>\n";
        
        if ($has_contact_link) $contact_link_found++;
        $total_pages_checked++;
    }
}

echo "<h3>Summary:</h3>\n";
echo "<p><strong>Pages with contact inquiries link:</strong> $contact_link_found / $total_pages_checked</p>\n";

if ($contact_link_found === $total_pages_checked) {
    echo "<div style='background: #dcfce7; border: 1px solid #bbf7d0; color: #16a34a; padding: 1rem; border-radius: 8px; margin: 1rem 0;'>\n";
    echo "<h3 style='margin: 0 0 0.5rem 0;'>✅ ALL TESTS PASSED!</h3>\n";
    echo "<p style='margin: 0;'>Contact inquiries navigation is properly implemented across all admin pages.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #fee2e2; border: 1px solid #fecaca; color: #dc2626; padding: 1rem; border-radius: 8px; margin: 1rem 0;'>\n";
    echo "<h3 style='margin: 0 0 0.5rem 0;'>⚠ SOME ISSUES FOUND</h3>\n";
    echo "<p style='margin: 0;'>Some admin pages are missing the contact inquiries navigation link.</p>\n";
    echo "</div>\n";
}

echo "<h3>Quick Access Links:</h3>\n";
echo "<ul>\n";
echo "<li><a href='dashboard.php' target='_blank'><strong>Admin Dashboard</strong></a> - Main admin panel</li>\n";
echo "<li><a href='contact_inquiries.php' target='_blank'><strong>Contact Inquiries</strong></a> - View and manage contact form submissions</li>\n";
echo "<li><a href='../index.php?page=contact' target='_blank'><strong>Contact Form</strong></a> - Test the public contact form</li>\n";
echo "</ul>\n";

echo "<h3>Features Available:</h3>\n";
echo "<ul>\n";
echo "<li>✅ Contact form submissions are stored in database</li>\n";
echo "<li>✅ Admin can view all contact inquiries</li>\n";
echo "<li>✅ Admin can update inquiry status (New, In Progress, Resolved, Closed)</li>\n";
echo "<li>✅ Contact inquiries are accessible from admin navigation menu</li>\n";
echo "<li>✅ Email links for direct contact with inquirers</li>\n";
echo "<li>✅ Inquiry type categorization</li>\n";
echo "<li>✅ IP address tracking for security</li>\n";
echo "<li>✅ Timestamp tracking for all inquiries</li>\n";
echo "</ul>\n";
?>
