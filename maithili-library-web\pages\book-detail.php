<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Book Detail Page
 */

$book_id = $_GET['id'] ?? '';

if (empty($book_id)) {
    header('Location: ?page=catalog');
    exit;
}

try {
    $db = new Database();
    
    // Get book details with all related information
    $book = $db->fetch("
        SELECT b.*, 
               a.name as author_name, a.name<PERSON><PERSON><PERSON> as author_name_nepali, a.biography as author_biography,
               c.name as category_name, c.name<PERSON><PERSON><PERSON> as category_name_nepali, c.description as category_description,
               l.name as language_name, l.nameNepali as language_name_nepali,
               p.name as publisher_name, p.name<PERSON><PERSON><PERSON> as publisher_name_nepali, p.address as publisher_address,
               s.name as subject_name, s.nameN<PERSON>ali as subject_name_nepali,
               ser.name as series_name, ser.nameNepali as series_name_nepali,
               loc.shelf, loc.row, loc.description as location_description,
               cond.name as condition_name, cond.description as condition_description, cond.color as condition_color,
               src.name as source_name, src.name<PERSON><PERSON><PERSON> as source_name_nepali
        FROM books b 
        LEFT JOIN authors a ON b.authorId = a.id 
        LEFT JOIN categories c ON b.categoryId = c.id 
        LEFT JOIN languages l ON b.languageId = l.id 
        LEFT JOIN publishers p ON b.publisherId = p.id 
        LEFT JOIN subjects s ON b.subjectId = s.id 
        LEFT JOIN book_series ser ON b.seriesId = ser.id 
        LEFT JOIN locations loc ON b.locationId = loc.id 
        LEFT JOIN conditions cond ON b.conditionId = cond.id 
        LEFT JOIN sources src ON b.sourceId = src.id 
        WHERE b.id = ? AND b.isDeleted = 0
    ", [$book_id]);
    
    if (!$book) {
        header('Location: ?page=catalog');
        exit;
    }
    
    // Get related books by same author
    $related_books = $db->fetchAll("
        SELECT b.id, b.title, b.titleNepali, b.publishedYear
        FROM books b 
        WHERE b.authorId = ? AND b.id != ? AND b.isDeleted = 0
        ORDER BY b.publishedYear DESC 
        LIMIT 5
    ", [$book['authorId'], $book_id]);
    
    $page_title = htmlspecialchars($book['titleNepali'] ?: $book['title']) . ' - ' . APP_NAME;
    $page_description = 'Book details for ' . htmlspecialchars($book['title']) . ' by ' . htmlspecialchars($book['author_name']);
    
} catch (Exception $e) {
    log_activity("Book detail error: " . $e->getMessage(), 'ERROR');
    header('Location: ?page=catalog');
    exit;
}

include 'templates/header.php';
?>

<main class="main-content">
    <!-- Book Detail Hero Section -->
    <section class="book-hero-section">
        <div class="container">
            <div class="book-hero-layout">
                <!-- Book Cover & Gallery -->
                <div class="book-cover-section">
                    <div class="book-cover-container">
                        <?php
                        $image_path = "uploads/books/" . $book['id'] . ".jpg";
                        if (file_exists($image_path)):
                        ?>
                            <div class="book-cover-wrapper">
                                <img src="<?= $image_path ?>?v=<?= filemtime($image_path) ?>"
                                     alt="<?= htmlspecialchars($book['title']) ?>"
                                     class="book-cover-image"
                                     data-book-title="<?= htmlspecialchars($book['titleNepali'] ?: $book['title']) ?>"
                                     data-book-title-english="<?= htmlspecialchars($book['title']) ?>"
                                     data-book-author="<?= htmlspecialchars($book['author_name_nepali'] ?: $book['author_name']) ?>"
                                     data-book-author-english="<?= htmlspecialchars($book['author_name']) ?>"
                                     data-book-year="<?= $book['publishedYear'] ?>"
                                     onclick="openBookImageModal(this)">
                                <div class="cover-overlay">
                                    <button class="zoom-btn" onclick="openBookImageModal(this.closest('.book-cover-wrapper').querySelector('.book-cover-image'))">
                                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                        </svg>
                                        View Full Size
                                    </button>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="book-cover-placeholder">
                                <div class="placeholder-content">
                                    <svg class="placeholder-icon" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                    </svg>
                                    <span>No Cover Image</span>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Book Condition Badge -->
                    <?php if ($book['condition_name']): ?>
                        <div class="book-status-section">
                            <div class="condition-badge" style="--condition-color: <?= $book['condition_color'] ?: '#6B7280' ?>">
                                <span class="condition-label">Condition:</span>
                                <span class="condition-value"><?= htmlspecialchars($book['condition_name']) ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Book Information -->
                <div class="book-info-section">
                    <!-- Title & Author Information Grid -->
                    <div class="book-title-info">
                        <div class="title-info-grid">
                            <!-- Title Card -->
                            <div class="title-info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Title</span>
                                    <div class="info-value title-value">
                                        <?php if ($book['titleNepali']): ?>
                                            <span class="title-nepali"><?= htmlspecialchars($book['titleNepali']) ?></span>
                                        <?php endif; ?>
                                        <span class="title-english"><?= htmlspecialchars($book['title']) ?></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Author Card -->
                            <div class="title-info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Author</span>
                                    <div class="info-value author-value">
                                        <?php if ($book['author_name_nepali']): ?>
                                            <span class="author-nepali"><?= htmlspecialchars($book['author_name_nepali']) ?></span>
                                        <?php endif; ?>
                                        <span class="author-english"><?= htmlspecialchars($book['author_name']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Key Information Grid -->
                    <div class="book-key-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Published</span>
                                    <span class="info-value"><?= $book['publishedYear'] ?></span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95c-.32-1.25-.78-2.45-1.38-3.56 1.84.63 3.37 1.91 4.33 3.56zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56-1.84-.63-3.37-1.9-4.33-3.56zm2.95-8H5.08c.96-1.66 2.49-2.93 4.33-3.56C8.81 5.55 8.35 6.75 8.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95c-.96 1.65-2.49 2.93-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Language</span>
                                    <span class="info-value"><?= htmlspecialchars($book['language_name_nepali'] ?: $book['language_name']) ?></span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Category</span>
                                    <span class="info-value"><?= htmlspecialchars($book['category_name_nepali'] ?: $book['category_name']) ?></span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Accession</span>
                                    <span class="info-value"><?= htmlspecialchars($book['accessionNo']) ?></span>
                                </div>
                            </div>

                            <?php if ($book['pages']): ?>
                            <div class="info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Pages</span>
                                    <span class="info-value"><?= number_format($book['pages']) ?></span>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if ($book['isbn']): ?>
                            <div class="info-item">
                                <div class="info-icon-wrapper">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M3 3h18v18H3V3zm16 16V5H5v14h14zM7 7h2v2H7V7zm0 4h2v2H7v-2zm0 4h2v2H7v-2zm4-8h6v2h-6V7zm0 4h6v2h-6v-2zm0 4h6v2h-6v-2z"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">ISBN</span>
                                    <span class="info-value"><?= htmlspecialchars($book['isbn']) ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Library Statistics -->
            <?php
            $stats = get_library_statistics($db);
            echo render_statistics_section($stats, 'page-stats');
            ?>
        </div>
    </section>

    <!-- Detailed Information Section -->
    <section class="book-details-section">
        <div class="container">
            <div class="details-layout">
                <!-- Main Details -->
                <div class="main-details">

                    <!-- Author Information -->
                    <?php if ($book['author_biography'] || $book['author_name']): ?>
                    <div class="detail-card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                                About the Author
                            </h2>
                        </div>
                        <div class="card-content">
                            <div class="author-info">
                                <h3 class="author-name">
                                    <?php if ($book['author_name_nepali']): ?>
                                        <span class="name-nepali"><?= htmlspecialchars($book['author_name_nepali']) ?></span>
                                    <?php endif; ?>
                                    <span class="name-english"><?= htmlspecialchars($book['author_name']) ?></span>
                                </h3>
                                <?php if ($book['author_biography']): ?>
                                    <div class="author-biography">
                                        <?= nl2br(htmlspecialchars($book['author_biography'])) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Publication Information -->
                    <div class="detail-card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                </svg>
                                Publication Details
                            </h2>
                        </div>
                        <div class="card-content">
                            <div class="publication-grid">
                                <?php if ($book['publisher_name']): ?>
                                <div class="pub-item">
                                    <span class="pub-label">Publisher</span>
                                    <div class="pub-value">
                                        <?php if ($book['publisher_name_nepali']): ?>
                                            <span class="value-nepali"><?= htmlspecialchars($book['publisher_name_nepali']) ?></span>
                                        <?php endif; ?>
                                        <span class="value-english"><?= htmlspecialchars($book['publisher_name']) ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if ($book['edition']): ?>
                                <div class="pub-item">
                                    <span class="pub-label">Edition</span>
                                    <span class="pub-value"><?= htmlspecialchars($book['edition']) ?></span>
                                </div>
                                <?php endif; ?>

                                <?php if ($book['subject_name']): ?>
                                <div class="pub-item">
                                    <span class="pub-label">Subject</span>
                                    <div class="pub-value">
                                        <?php if ($book['subject_name_nepali']): ?>
                                            <span class="value-nepali"><?= htmlspecialchars($book['subject_name_nepali']) ?></span>
                                        <?php endif; ?>
                                        <span class="value-english"><?= htmlspecialchars($book['subject_name']) ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if ($book['series_name']): ?>
                                <div class="pub-item">
                                    <span class="pub-label">Series</span>
                                    <div class="pub-value">
                                        <?php if ($book['series_name_nepali']): ?>
                                            <span class="value-nepali"><?= htmlspecialchars($book['series_name_nepali']) ?></span>
                                        <?php endif; ?>
                                        <span class="value-english"><?= htmlspecialchars($book['series_name']) ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Notes -->
                    <?php if ($book['remark']): ?>
                    <div class="detail-card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14,10H19.5L14,4.5V10M5,3H15L21,9V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5C3,3.89 3.89,3 5,3M9,12H16V14H9V12M9,16H14V18H9V16Z"/>
                                </svg>
                                Additional Notes
                            </h2>
                        </div>
                        <div class="card-content">
                            <div class="notes-content">
                                <?= nl2br(htmlspecialchars($book['remark'])) ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar Information -->
                <div class="sidebar-details">
                    <!-- Library Information -->
                    <div class="sidebar-card">
                        <h3 class="sidebar-title">Library Information</h3>
                        <div class="sidebar-content">
                            <div class="sidebar-item">
                                <span class="sidebar-label">Accession Number</span>
                                <span class="sidebar-value"><?= htmlspecialchars($book['accessionNo']) ?></span>
                            </div>

                            <?php if ($book['shelf'] || $book['row']): ?>
                            <div class="sidebar-item">
                                <span class="sidebar-label">Location</span>
                                <span class="sidebar-value">
                                    <?php if ($book['shelf']): ?>Shelf <?= htmlspecialchars($book['shelf']) ?><?php endif; ?>
                                    <?php if ($book['row']): ?><?= $book['shelf'] ? ', ' : '' ?>Row <?= htmlspecialchars($book['row']) ?><?php endif; ?>
                                </span>
                            </div>
                            <?php endif; ?>

                            <?php if ($book['source_name']): ?>
                            <div class="sidebar-item">
                                <span class="sidebar-label">Source</span>
                                <span class="sidebar-value"><?= htmlspecialchars($book['source_name_nepali'] ?: $book['source_name']) ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="sidebar-card">
                        <h3 class="sidebar-title">Quick Actions</h3>
                        <div class="sidebar-content">
                            <div class="action-buttons">
                                <a href="?page=catalog&category=<?= $book['categoryId'] ?>" class="action-button">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
                                    </svg>
                                    More in Category
                                </a>
                                <a href="?page=catalog&author=<?= $book['authorId'] ?>" class="action-button">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                    </svg>
                                    More by Author
                                </a>
                                <a href="?page=gallery" class="action-button">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                                    </svg>
                                    View Gallery
                                </a>
                                <a href="?page=catalog" class="action-button primary">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                                    </svg>
                                    Back to Catalog
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Books -->
    <?php if (!empty($related_books)): ?>
        <section class="related-books-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">
                        <svg class="section-icon" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
                        </svg>
                        More Books by <?= htmlspecialchars($book['author_name_nepali'] ?: $book['author_name']) ?>
                    </h2>
                    <p class="section-subtitle">Discover other works by this author</p>
                </div>
                <div class="related-books-grid">
                    <?php foreach ($related_books as $related_book): ?>
                        <div class="related-book-card">
                            <div class="related-book-image">
                                <?php
                                $related_image_path = "uploads/books/" . $related_book['id'] . ".jpg";
                                if (file_exists($related_image_path)):
                                ?>
                                    <img src="<?= $related_image_path ?>?v=<?= filemtime($related_image_path) ?>"
                                         alt="<?= htmlspecialchars($related_book['title']) ?>"
                                         class="related-book-img">
                                <?php else: ?>
                                    <div class="related-book-placeholder">
                                        <svg class="placeholder-icon" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="related-book-content">
                                <h4 class="related-book-title">
                                    <?= htmlspecialchars($related_book['titleNepali'] ?: $related_book['title']) ?>
                                </h4>
                                <p class="related-book-year"><?= $related_book['publishedYear'] ?></p>
                                <a href="?page=book&id=<?= $related_book['id'] ?>" class="related-book-link">
                                    <svg class="link-icon" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                                    </svg>
                                    View Details
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>
</main>

<script>
// Enhanced Book Detail JavaScript
function openBookImageModal(img) {
    // Check if img element exists
    if (!img || !img.src) {
        console.error('Invalid image element passed to openBookImageModal');
        return;
    }

    // Get book information from data attributes
    const bookTitle = img.dataset.bookTitle || img.alt || 'Book Title';
    const bookTitleEnglish = img.dataset.bookTitleEnglish || '';
    const bookAuthor = img.dataset.bookAuthor || '';
    const bookAuthorEnglish = img.dataset.bookAuthorEnglish || '';
    const bookYear = img.dataset.bookYear || '';

    // Create enhanced modal for book detail page
    const modal = document.createElement('div');
    modal.className = 'image-modal book-detail-modal';
    modal.innerHTML = `
        <div class="image-modal-overlay">
            <div class="image-modal-content">
                <div class="modal-image-container">
                    <img src="${img.src}" alt="${img.alt}" class="modal-image">
                </div>
                <div class="modal-info">
                    <div class="modal-book-details">
                        <h3 class="modal-title">
                            ${bookTitle}
                            ${bookTitleEnglish && bookTitleEnglish !== bookTitle ? `<span class="modal-title-english">${bookTitleEnglish}</span>` : ''}
                        </h3>
                        ${bookAuthor ? `
                            <div class="modal-author">
                                <span class="author-label">by</span>
                                <span class="author-name">
                                    ${bookAuthor}
                                    ${bookAuthorEnglish && bookAuthorEnglish !== bookAuthor ? `<span class="author-english">${bookAuthorEnglish}</span>` : ''}
                                </span>
                            </div>
                        ` : ''}
                        ${bookYear ? `<div class="modal-year">Published: ${bookYear}</div>` : ''}
                    </div>
                    <p class="modal-subtitle">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" style="margin-right: 0.5rem;">
                            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                        </svg>
                        Book Cover Image
                    </p>
                </div>
                <button class="image-modal-close" onclick="closeBookImageModal()">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';

    // Add event listeners
    modal.addEventListener('click', (e) => {
        if (e.target === modal || e.target.classList.contains('image-modal-overlay')) {
            closeBookImageModal();
        }
    });

    // Add keyboard support
    document.addEventListener('keydown', handleModalKeydown);

    // Animate in
    requestAnimationFrame(() => {
        modal.style.opacity = '1';
    });
}

function closeBookImageModal() {
    const modal = document.querySelector('.book-detail-modal');
    if (modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.remove();
            document.body.style.overflow = '';
            document.removeEventListener('keydown', handleModalKeydown);
        }, 200);
    }
}

function handleModalKeydown(e) {
    if (e.key === 'Escape') {
        closeBookImageModal();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Book detail page JavaScript loaded');
});
</script>

<style>
/* Modern Book Detail Styles */
:root {
    --book-detail-bg: #fafafa;
    --card-bg: #ffffff;
    --text-primary: #1a1a1a;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Book Hero Section */
.book-hero-section {
    background: var(--book-detail-bg);
    padding: var(--spacing-xl) 0 var(--spacing-2xl) 0;
}

.book-hero-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-2xl);
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Book Cover Section */
.book-cover-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.book-cover-container {
    position: relative;
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.book-cover-wrapper {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--book-detail-bg);
}

.book-cover-image {
    width: 100%;
    height: auto;
    display: block;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.book-cover-image:hover {
    transform: scale(1.02);
}

.cover-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: var(--spacing-lg);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.book-cover-wrapper:hover .cover-overlay {
    opacity: 1;
}

.zoom-btn {
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    background: white;
    transform: translateY(-1px);
}

.book-cover-placeholder {
    width: 100%;
    height: 500px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--border-color);
}

.placeholder-content {
    text-align: center;
    color: var(--text-muted);
}

.placeholder-icon {
    width: 4rem;
    height: 4rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* Book Status Section */
.book-status-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.condition-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.condition-label {
    color: var(--text-secondary);
}

.condition-value {
    color: var(--condition-color, var(--text-primary));
    font-weight: 500;
}

/* Book Information Section */
.book-info-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Title Information Grid - Matching Info Grid Style */
.book-title-info {
    background: var(--card-bg);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.title-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.title-info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--book-detail-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.title-info-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Title Value Styling */
.title-value {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.title-value .title-nepali {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: var(--devanagari-font);
    line-height: 1.3;
}

.title-value .title-english {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;
}

/* Author Value Styling */
.author-value {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.author-value .author-nepali {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--accent-color);
    font-family: var(--devanagari-font);
    line-height: 1.3;
}

.author-value .author-english {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Key Information Grid */
.book-key-info {
    background: var(--card-bg);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--book-detail-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.info-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.info-icon-wrapper {
    flex-shrink: 0;
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-content {
    flex: 1;
    min-width: 0;
}

.info-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    word-break: break-word;
}

/* Detailed Information Section */
.book-details-section {
    background: var(--card-bg);
    padding: var(--spacing-lg) 0 var(--spacing-2xl) 0;
}

.details-layout {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: var(--spacing-2xl);
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Detail Cards */
.detail-card {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    transition: all 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #f8f9fa 100%);
    color: var(--text-primary);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.card-content {
    padding: var(--spacing-xl);
}

/* Author Information */
.author-info {
    text-align: left;
}

.author-name {
    margin-bottom: var(--spacing-lg);
}

.name-nepali {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
    font-family: var(--devanagari-font);
}

.name-english {
    display: block;
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.author-biography {
    line-height: 1.7;
    color: var(--text-primary);
    background: var(--book-detail-bg);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-color);
}

/* Publication Grid */
.publication-grid {
    display: grid;
    gap: var(--spacing-lg);
}

.pub-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.pub-item:last-child {
    border-bottom: none;
}

.pub-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.pub-value {
    font-size: 1rem;
    color: var(--text-primary);
}

.value-nepali {
    display: block;
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
    font-family: var(--devanagari-font);
}

.value-english {
    color: var(--text-secondary);
}

/* Notes Content */
.notes-content {
    line-height: 1.7;
    color: var(--text-primary);
    background: var(--book-detail-bg);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--accent-color);
}

/* Sidebar */
.sidebar-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.sidebar-card {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.sidebar-title {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #f8f9fa 100%);
    color: var(--primary-color);
    padding: var(--spacing-lg);
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-content {
    padding: var(--spacing-lg);
}

.sidebar-item {
    display: flex;
    justify-content: space-between;
    align-items: start;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
    gap: var(--spacing-md);
}

.sidebar-item:last-child {
    border-bottom: none;
}

.sidebar-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    flex-shrink: 0;
}

.sidebar-value {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 600;
    text-align: right;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    color: var(--text-primary);
}

.action-button:hover {
    background: var(--book-detail-bg);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.action-button.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.action-button.primary:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .book-hero-layout {
        grid-template-columns: 350px 1fr;
        gap: var(--spacing-xl);
    }

    .details-layout {
        grid-template-columns: 1fr 300px;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 1024px) {
    .book-hero-layout {
        grid-template-columns: 300px 1fr;
        gap: var(--spacing-lg);
    }

    .details-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .sidebar-details {
        order: -1;
    }

    .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .book-hero-section {
        padding: var(--spacing-lg) 0 var(--spacing-xl) 0;
    }

    .book-details-section {
        padding: var(--spacing-md) 0 var(--spacing-xl) 0;
    }

    .book-hero-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .book-cover-section {
        order: 1;
    }

    .book-info-section {
        order: 2;
    }

    .book-cover-container {
        max-width: 300px;
        margin: 0 auto;
    }

    .title-nepali {
        font-size: 2rem;
    }

    .title-english {
        font-size: 1.5rem;
    }

    .author-nepali {
        font-size: 1.25rem;
    }

    .author-english {
        font-size: 1.125rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .info-item {
        padding: var(--spacing-sm);
    }

    .book-title-info,
    .book-key-info,
    .detail-card,
    .sidebar-card {
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: var(--radius-lg);
    }

    .title-info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .title-info-item {
        padding: var(--spacing-sm);
    }

    .card-content,
    .sidebar-content {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .book-hero-section {
        padding: var(--spacing-md) 0 var(--spacing-lg) 0;
    }

    .book-details-section {
        padding: var(--spacing-sm) 0 var(--spacing-lg) 0;
    }

    .book-cover-container {
        padding: var(--spacing-md);
    }

    .book-title-info,
    .book-key-info {
        padding: var(--spacing-lg);
    }

    .title-value .title-nepali {
        font-size: 1.25rem;
    }

    .title-value .title-english {
        font-size: 1rem;
    }

    .author-value .author-nepali {
        font-size: 1.125rem;
    }

    .author-value .author-english {
        font-size: 0.875rem;
    }

    .title-nepali {
        font-size: 1.75rem;
    }

    .title-english {
        font-size: 1.25rem;
    }

    .author-nepali {
        font-size: 1.125rem;
    }

    .author-english {
        font-size: 1rem;
    }

    .info-icon-wrapper {
        width: 2rem;
        height: 2rem;
    }

    .info-icon-wrapper svg {
        width: 16px;
        height: 16px;
    }


}

/* Large Screen Optimizations */
@media (min-width: 1440px) {
    .book-hero-layout {
        grid-template-columns: 450px 1fr;
        gap: var(--spacing-2xl);
    }

    .details-layout {
        grid-template-columns: 1fr 400px;
        gap: var(--spacing-2xl);
    }

    .title-nepali {
        font-size: 3rem;
    }

    .title-english {
        font-size: 2.5rem;
    }
}

@media (min-width: 1920px) {
    .container {
        max-width: 1600px;
    }

    .book-hero-layout,
    .details-layout {
        max-width: 1600px;
    }
}





.section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: var(--primary-color);
    font-size: 2.25rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.section-icon {
    width: 2rem;
    height: 2rem;
}

.section-subtitle {
    color: var(--text-light);
    font-size: 1.125rem;
    margin: 0;
}

.related-books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.related-book-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--medium-gray);
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.related-book-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.related-book-image {
    height: 200px;
    overflow: hidden;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

.related-book-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.related-book-card:hover .related-book-img {
    transform: scale(1.1);
}

.related-book-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--medium-gray) 100%);
    color: var(--dark-gray);
}

.placeholder-icon {
    width: 3rem;
    height: 3rem;
    opacity: 0.5;
}

.related-book-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-book-title {
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    margin: 0;
    flex: 1;
}

.related-book-year {
    color: var(--text-light);
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
}

.related-book-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.related-book-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.link-icon {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .book-detail-layout {
        grid-template-columns: 320px 1fr;
        gap: 2rem;
    }
}

@media (max-width: 1024px) {
    .book-detail-layout {
        grid-template-columns: 300px 1fr;
        gap: 1.5rem;
    }

    .book-title .nepali-text {
        font-size: 2.25rem;
    }

    .book-title .english-text {
        font-size: 1.75rem;
    }

    .related-books-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
}

/* Tablet Portrait Specific */
@media (max-width: 900px) and (min-width: 769px) {
    .book-detail-layout {
        grid-template-columns: 280px 1fr;
        gap: 1.25rem;
    }

    .quick-info-card {
        padding: 1.25rem;
    }

    .quick-info-item {
        padding: 0.5rem 0;
    }

    .info-value,
    .info-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .book-detail-section {
        padding: 1rem 0 2rem;
    }

    .book-detail-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .book-image-column {
        order: 1;
        position: static;
        height: auto;
        max-height: none;
        overflow-y: visible;
    }

    .book-main-content {
        order: 2;
    }

    .book-image-container {
        text-align: center;
    }

    .book-detail-image,
    .book-detail-placeholder {
        max-width: 100%;
        width: 100%;
        margin: 0 auto;
    }

    .book-detail-placeholder {
        height: 300px;
    }

    .quick-info-card {
        max-width: 100%;
        width: 100%;
        margin: 0;
    }

    .book-header {
        padding: 1.5rem;
    }

    .book-title .nepali-text {
        font-size: 2rem;
    }

    .book-title .english-text {
        font-size: 1.5rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .book-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .action-btn {
        justify-content: center;
    }

    .related-books-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .section-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.75rem;
    }

    .book-detail-image,
    .book-detail-placeholder {
        max-width: 100%;
        width: 100%;
    }

    .book-detail-placeholder {
        height: 250px;
    }

    .book-title .nepali-text {
        font-size: 1.75rem;
    }

    .book-title .english-text {
        font-size: 1.25rem;
    }

    .meta-tag {
        font-size: 0.75rem;
        padding: 0.5rem 1rem;
    }

    .card-header,
    .card-content {
        padding: 1rem;
    }

    .quick-info-card {
        padding: 1rem;
    }

    .quick-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        padding: 0.5rem 0;
    }

    .info-value {
        text-align: left;
        font-size: 0.875rem;
    }

    .info-label {
        font-size: 0.75rem;
    }

    .book-actions {
        padding: 1.5rem;
    }

    .action-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.75rem;
    }
}

/* Large Screen Optimizations */
@media (min-width: 1440px) {
    .book-detail-layout {
        grid-template-columns: 400px 1fr;
        gap: 3rem;
    }

    .book-title .nepali-text {
        font-size: 3rem;
    }

    .book-title .english-text {
        font-size: 2.5rem;
    }

    .related-books-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2.5rem;
    }
}

@media (min-width: 1920px) {
    .container {
        max-width: 1600px;
    }

    .book-detail-layout {
        grid-template-columns: 450px 1fr;
        gap: 4rem;
    }
}

/* Enhanced Book Detail Modal */
.book-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 2rem;
}

.book-detail-modal .image-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.book-detail-modal .image-modal-content {
    position: relative;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: row;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    min-height: 500px;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
}

.modal-image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: var(--radius-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.modal-info {
    flex: 0 0 380px;
    padding: var(--spacing-xl);
    background: var(--card-bg);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    overflow-y: auto;
    max-height: 90vh;
}

.modal-title {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1.3;
    font-family: var(--devanagari-font);
}

.modal-title-english {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-top: 0.25rem;
    font-family: inherit;
}

.modal-author {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
}

.author-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-style: italic;
}

.author-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--accent-color);
    font-family: var(--devanagari-font);
}

.author-english {
    display: block;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-top: 0.125rem;
    font-family: inherit;
}

.modal-year {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.book-detail-modal .image-modal-close {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10001;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.book-detail-modal .image-modal-close:hover {
    background: rgba(0, 0, 0, 0.95);
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Enhanced Modal Styling */
.modal-book-details {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-subtitle {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    padding-top: var(--spacing-sm);
    font-style: italic;
}

/* Desktop Modal Styles */
@media (min-width: 1024px) {
    .book-detail-modal .image-modal-content {
        max-width: 1400px;
        min-height: 600px;
    }

    .modal-image-container {
        min-height: 600px;
    }

    .modal-info {
        flex: 0 0 420px;
        padding: var(--spacing-2xl);
    }

    .modal-title {
        font-size: 1.75rem;
    }

    .modal-title-english {
        font-size: 1.5rem;
    }

    .author-name {
        font-size: 1.25rem;
    }
}

/* Tablet Modal Styles */
@media (max-width: 1023px) and (min-width: 769px) {
    .book-detail-modal .image-modal-content {
        flex-direction: column;
        max-width: 90vw;
        max-height: 90vh;
    }

    .modal-image-container {
        flex: 1;
        min-height: 400px;
        max-height: 60vh;
    }

    .modal-info {
        flex: 0 0 auto;
        border-left: none;
        border-top: 1px solid var(--border-color);
        padding: var(--spacing-lg);
        max-height: 40vh;
        overflow-y: auto;
    }
}

/* Mobile Modal Styles */
@media (max-width: 768px) {
    .book-detail-modal {
        padding: 1rem;
    }

    .book-detail-modal .image-modal-content {
        flex-direction: column;
        max-width: 95vw;
        max-height: 95vh;
        margin: 0;
    }

    .modal-image-container {
        flex: 1;
        min-height: 300px;
        max-height: 60vh;
    }

    .modal-info {
        flex: 0 0 auto;
        border-left: none;
        border-top: 1px solid var(--border-color);
        padding: var(--spacing-md);
        max-height: 40vh;
        overflow-y: auto;
    }

    .modal-title {
        font-size: 1.25rem;
    }

    .modal-title-english {
        font-size: 1.125rem;
    }

    .modal-author {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .author-name {
        font-size: 1rem;
    }

    .author-english {
        font-size: 0.875rem;
    }

    .book-detail-modal .image-modal-close {
        top: var(--spacing-md);
        right: var(--spacing-md);
        width: 2.5rem;
        height: 2.5rem;
    }
}

/* Print Styles */
@media print {
    .breadcrumb-nav,
    .book-status-section,
    .sidebar-details,
    .action-buttons {
        display: none;
    }

    .book-hero-layout,
    .details-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .book-cover-container {
        max-width: 200px;
        margin: 0 auto var(--spacing-lg);
    }

    .detail-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
        margin-bottom: var(--spacing-lg);
    }

    .card-header {
        background: #f5f5f5 !important;
        color: #333 !important;
    }
}



/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .book-cover-image,
    .detail-card,
    .sidebar-card {
        border: 2px solid currentColor;
    }

    .action-button {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .book-cover-image:hover,
    .detail-card:hover,
    .info-item:hover,
    .action-button:hover {
        transform: none;
    }
}
</style>

<?php include 'templates/footer.php'; ?>
